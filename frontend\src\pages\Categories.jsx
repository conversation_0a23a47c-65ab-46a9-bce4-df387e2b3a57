import { useState, useEffect } from 'react'
import { Plus, Palette, Tag, Edit, Trash2 } from 'lucide-react'
import toast from 'react-hot-toast'
import api from '../services/api'
import CategoryModal from '../components/CategoryModal'
import CategoryList from '../components/CategoryList'
import TagModal from '../components/TagModal'

function Categories() {
  const [categories, setCategories] = useState([])
  const [tags, setTags] = useState([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingCategory, setEditingCategory] = useState(null)
  const [showTagModal, setShowTagModal] = useState(false)
  const [editingTag, setEditingTag] = useState(null)

  useEffect(() => {
    fetchCategories()
    fetchTags()
  }, [])

  const fetchCategories = async () => {
    try {
      setLoading(true)
      const response = await api.get('/categories')
      setCategories(response.data)
    } catch (error) {
      console.error('Erro ao buscar categorias:', error)
      toast.error('Erro ao carregar categorias')
    } finally {
      setLoading(false)
    }
  }

  const fetchTags = async () => {
    try {
      const response = await api.get('/tags')
      setTags(response.data)
    } catch (error) {
      console.error('Erro ao buscar tags:', error)
      toast.error('Erro ao carregar tags')
    }
  }

  const handleEdit = (category) => {
    setEditingCategory(category)
    setShowModal(true)
  }

  const handleEditTag = (tag) => {
    setEditingTag(tag)
    setShowTagModal(true)
  }

  const handleCreateSubcategory = (parentCategory) => {
    setEditingCategory({ parentId: parentCategory.id })
    setShowModal(true)
  }

  const handleDelete = async (category) => {
    if (window.confirm('Tem certeza que deseja deletar esta categoria?')) {
      try {
        await api.delete(`/categories/${category.id}`)
        toast.success('Categoria deletada com sucesso!')
        fetchCategories()
      } catch (error) {
        console.error('Erro ao deletar categoria:', error)
        toast.error(error.response?.data?.error || 'Erro ao deletar categoria')
      }
    }
  }

  const handleDeleteTag = async (id) => {
    if (window.confirm('Tem certeza que deseja deletar esta tag?')) {
      try {
        await api.delete(`/tags/${id}`)
        toast.success('Tag deletada com sucesso!')
        fetchTags()
      } catch (error) {
        console.error('Erro ao deletar tag:', error)
        toast.error(error.response?.data?.error || 'Erro ao deletar tag')
      }
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-2xl border shadow-lg p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Categorias & Tags</h1>
            <p className="text-gray-600 mt-1">Organize suas transações com categorias e tags personalizadas</p>
          </div>

          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={() => {
                setEditingCategory(null)
                setShowModal(true)
              }}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="h-4 w-4" />
              Nova Categoria
            </button>
            <button
              onClick={() => {
                setEditingTag(null)
                setShowTagModal(true)
              }}
              className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              <Tag className="h-4 w-4" />
              Nova Tag
            </button>
          </div>
        </div>
      </div>

      {/* Categorias */}
      <div className="bg-white rounded-2xl shadow-lg border p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Categorias</h2>
            <p className="text-gray-600 mt-1">Organize suas transações em categorias principais</p>
          </div>
        </div>

        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : categories.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">Nenhuma categoria encontrada</p>
          </div>
        ) : (
          <CategoryList
            categories={categories}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onCreateSubcategory={handleCreateSubcategory}
          />
        )}
      </div>

      {/* Tags */}
      <div className="bg-white rounded-2xl shadow-lg border p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Tags</h2>
            <p className="text-gray-600 mt-1">Adicione tags opcionais às suas transações para filtros mais específicos</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {tags.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <p className="text-gray-500">Nenhuma tag encontrada</p>
            </div>
          ) : (
            tags.map((tag) => (
              <div key={tag.id} className="card hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">

                  
                    <div 
                      className="w-10 h-10 rounded-full flex items-center justify-center text-white text-lg"
                      style={{ backgroundColor: tag.color }}
                    >
                      <Tag className="h-5 w-5" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-lg font-medium text-gray-900">{tag.name}</h3>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleEditTag(tag)}
                      className="text-primary-600 hover:text-primary-900 p-1"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteTag(tag.id)}
                      className="text-red-600 hover:text-red-900 p-1"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
                
                <div className="flex items-center text-sm text-gray-500">
                  <Palette className="h-4 w-4 mr-1" />
                  <span>{tag.color}</span>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Modais */}
      <CategoryModal
        isOpen={showModal}
        onClose={() => {
          setShowModal(false)
          setEditingCategory(null)
        }}
        category={editingCategory}
        categories={categories}
        onSuccess={fetchCategories}
      />

      <TagModal
        isOpen={showTagModal}
        onClose={() => {
          setShowTagModal(false)
          setEditingTag(null)
        }}
        tag={editingTag}
        onSuccess={fetchTags}
      />
    </div>
  )
}

export default Categories
