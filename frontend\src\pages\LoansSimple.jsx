import React, { useState, useEffect } from 'react'
import { Plus, Users, DollarSign, TrendingUp, TrendingDown } from 'lucide-react'
import toast from 'react-hot-toast'
import Sidebar from '../components/Sidebar'

function LoansSimple() {
  const [loading, setLoading] = useState(false)
  const [contacts, setContacts] = useState([])

  useEffect(() => {
    // Simular carregamento
    setLoading(true)
    setTimeout(() => {
      setContacts([
        {
          id: '1',
          name: '<PERSON>',
          email: '<EMAIL>',
          phone: '(11) 99999-9999',
          status: 'GOOD',
          totalLoans: 2,
          paidOnTime: 5,
          latePayments: 0,
          loans: []
        },
        {
          id: '2',
          name: '<PERSON>',
          email: '<EMAIL>',
          phone: '(11) 88888-8888',
          status: 'NEUTRAL',
          totalLoans: 1,
          paidOnTime: 2,
          latePayments: 1,
          loans: []
        }
      ])
      setLoading(false)
    }, 1000)
  }, [])

  const getStatusLabel = (status) => {
    const labels = {
      'GOOD': 'Bom Pagador',
      'NEUTRAL': 'Neutro',
      'BAD': 'Mau Pagador'
    }
    return labels[status] || status
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'GOOD':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'BAD':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Carregando empréstimos...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      
      <div className="flex-1 overflow-auto">
        <div className="p-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Empréstimos e Dívidas</h1>
                <p className="text-gray-600 mt-1">Gerencie seus empréstimos e contratos</p>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={() => toast.success('Funcionalidade em desenvolvimento')}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Users className="h-4 w-4" />
                  Novo Contato
                </button>
                <button
                  onClick={() => toast.success('Funcionalidade em desenvolvimento')}
                  className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Plus className="h-4 w-4" />
                  Novo Empréstimo
                </button>
              </div>
            </div>

            {/* Estatísticas */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Emprestei</p>
                    <p className="text-2xl font-bold text-red-600">R$ 5.000,00</p>
                  </div>
                  <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                    <TrendingDown className="h-6 w-6 text-red-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Peguei Emprestado</p>
                    <p className="text-2xl font-bold text-green-600">R$ 2.000,00</p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Empréstimos Ativos</p>
                    <p className="text-2xl font-bold text-blue-600">3</p>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <DollarSign className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Quitados</p>
                    <p className="text-2xl font-bold text-gray-600">2</p>
                  </div>
                  <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                    <DollarSign className="h-6 w-6 text-gray-600" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Lista de Contatos */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {contacts.map((contact) => (
              <div key={contact.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                {/* Header com foto e nome */}
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      {contact.name}
                    </h3>
                    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(contact.status)}`}>
                      {getStatusLabel(contact.status)}
                    </div>
                  </div>
                </div>

                {/* Informações de contato */}
                <div className="space-y-2 mb-4">
                  {contact.email && (
                    <p className="text-sm text-gray-600">📧 {contact.email}</p>
                  )}
                  {contact.phone && (
                    <p className="text-sm text-gray-600">📞 {contact.phone}</p>
                  )}
                </div>

                {/* Estatísticas de pagamento */}
                {contact.totalLoans > 0 && (
                  <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div>
                        <p className="text-xs text-gray-600">Em dia</p>
                        <p className="text-sm font-semibold text-green-600">{contact.paidOnTime}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-600">Atrasados</p>
                        <p className="text-sm font-semibold text-red-600">{contact.latePayments}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Ações */}
                <div className="flex gap-2">
                  <button
                    onClick={() => toast.success('Funcionalidade em desenvolvimento')}
                    className="flex-1 px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors"
                  >
                    Ver
                  </button>
                  <button
                    onClick={() => toast.success('Funcionalidade em desenvolvimento')}
                    className="px-3 py-2 text-sm bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors"
                  >
                    Empréstimo
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Mensagem de desenvolvimento */}
          <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-xl">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                🚧 Sistema em Desenvolvimento
              </h3>
              <p className="text-blue-700 mb-4">
                Esta é uma versão simplificada para teste. As funcionalidades completas estão sendo implementadas.
              </p>
              <div className="text-sm text-blue-600">
                <p>✅ Interface básica funcionando</p>
                <p>🔄 Modais em desenvolvimento</p>
                <p>🔄 Integração com backend em desenvolvimento</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LoansSimple
