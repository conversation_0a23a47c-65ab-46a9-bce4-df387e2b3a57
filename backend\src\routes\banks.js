const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Listar bancos do usuário
router.get('/', async (req, res) => {
  try {
    const banks = await prisma.bank.findMany({
      where: { userId: req.user.id },
      orderBy: { name: 'asc' }
    });

    res.json(banks);
  } catch (error) {
    console.error('Erro ao buscar bancos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar banco
router.post('/', async (req, res) => {
  try {
    const { name, icon, color, initialBalance, billDueDay } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Nome é obrigatório' });
    }

    const bank = await prisma.bank.create({
      data: {
        name,
        icon: icon || '🏦',
        color: color || '#3B82F6',
        initialBalance: initialBalance || 0,
        currentBalance: initialBalance || 0,
        billDueDay: billDueDay || null,
        userId: req.user.id
      }
    });

    res.status(201).json(bank);
  } catch (error) {
    console.error('Erro ao criar banco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar banco
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, icon, color, isVisible, billDueDay } = req.body;

    const bank = await prisma.bank.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    const updatedBank = await prisma.bank.update({
      where: { id },
      data: {
        name: name || bank.name,
        icon: icon || bank.icon,
        color: color || bank.color,
        isVisible: isVisible !== undefined ? isVisible : bank.isVisible,
        billDueDay: billDueDay !== undefined ? billDueDay : bank.billDueDay
      }
    });

    res.json(updatedBank);
  } catch (error) {
    console.error('Erro ao atualizar banco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar banco
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const bank = await prisma.bank.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    // Verificar se há transações vinculadas
    const transactionCount = await prisma.transaction.count({
      where: { bankId: id }
    });

    if (transactionCount > 0) {
      return res.status(400).json({
        error: 'Não é possível excluir banco com transações vinculadas'
      });
    }

    await prisma.bank.delete({
      where: { id }
    });

    res.json({ message: 'Banco excluído com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir banco:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Obter saldo total
router.get('/balance', async (req, res) => {
  try {
    const banks = await prisma.bank.findMany({
      where: {
        userId: req.user.id,
        isVisible: true
      }
    });

    const totalBalance = banks.reduce((sum, bank) => sum + bank.currentBalance, 0);
    const bankCount = banks.length;

    res.json({
      totalBalance,
      bankCount,
      banks: banks.map(bank => ({
        id: bank.id,
        name: bank.name,
        icon: bank.icon,
        color: bank.color,
        currentBalance: bank.currentBalance
      }))
    });
  } catch (error) {
    console.error('Erro ao buscar saldo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Transferir entre bancos
router.post('/transfer', async (req, res) => {
  try {
    const { fromBankId, toBankId, amount, description } = req.body;

    if (!fromBankId || !toBankId || !amount || amount <= 0) {
      return res.status(400).json({ error: 'Dados inválidos para transferência' });
    }

    if (fromBankId === toBankId) {
      return res.status(400).json({ error: 'Banco de origem e destino devem ser diferentes' });
    }

    // Verificar se os bancos existem e pertencem ao usuário
    const [fromBank, toBank] = await Promise.all([
      prisma.bank.findFirst({ where: { id: fromBankId, userId: req.user.id } }),
      prisma.bank.findFirst({ where: { id: toBankId, userId: req.user.id } })
    ]);

    if (!fromBank || !toBank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    if (fromBank.currentBalance < amount) {
      return res.status(400).json({ error: 'Saldo insuficiente' });
    }

    // Realizar transferência em transação
    await prisma.$transaction(async (tx) => {
      // Debitar do banco origem
      await tx.bank.update({
        where: { id: fromBankId },
        data: { currentBalance: fromBank.currentBalance - amount }
      });

      // Creditar no banco destino
      await tx.bank.update({
        where: { id: toBankId },
        data: { currentBalance: toBank.currentBalance + amount }
      });

      // Criar transação de saída
      await tx.transaction.create({
        data: {
          description: description || `Transferência para ${toBank.name}`,
          amount,
          type: 'EXPENSE',
          userId: req.user.id,
          bankId: fromBankId
        }
      });

      // Criar transação de entrada
      await tx.transaction.create({
        data: {
          description: description || `Transferência de ${fromBank.name}`,
          amount,
          type: 'INCOME',
          userId: req.user.id,
          bankId: toBankId
        }
      });
    });

    res.json({ message: 'Transferência realizada com sucesso' });
  } catch (error) {
    console.error('Erro ao realizar transferência:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
