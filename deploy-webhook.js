#!/usr/bin/env node

/**
 * Webhook Listener para Auto-Deploy
 * 
 * Este script escuta webhooks do GitHub e automaticamente
 * atualiza os containers Docker quando há push na branch develop
 */

const express = require('express');
const crypto = require('crypto');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = process.env.WEBHOOK_PORT || 9000;
const SECRET = process.env.WEBHOOK_SECRET || 'your-webhook-secret';
const DEPLOY_BRANCH = process.env.DEPLOY_BRANCH || 'develop';

app.use(express.json());

// Middleware para verificar assinatura do GitHub
function verifySignature(req, res, next) {
  const signature = req.headers['x-hub-signature-256'];
  const payload = JSON.stringify(req.body);
  const expectedSignature = `sha256=${crypto
    .createHmac('sha256', SECRET)
    .update(payload)
    .digest('hex')}`;

  if (signature !== expectedSignature) {
    console.log('❌ Invalid signature');
    return res.status(401).send('Unauthorized');
  }

  next();
}

// Função para executar comandos
function executeCommand(command, description) {
  return new Promise((resolve, reject) => {
    console.log(`🔄 ${description}...`);
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ ${description} failed:`, error);
        reject(error);
      } else {
        console.log(`✅ ${description} completed`);
        if (stdout) console.log(stdout);
        resolve(stdout);
      }
    });
  });
}

// Função principal de deploy
async function deployApplication() {
  try {
    const timestamp = new Date().toISOString();
    console.log(`\n🚀 Starting deployment at ${timestamp}`);

    // 1. Pull latest changes
    await executeCommand('git pull origin develop', 'Pulling latest changes');

    // 2. Stop containers
    await executeCommand('docker-compose down', 'Stopping containers');

    // 3. Pull latest images
    await executeCommand('docker-compose pull', 'Pulling latest images');

    // 4. Rebuild and start containers
    await executeCommand(
      'docker-compose up -d --build --force-recreate',
      'Rebuilding and starting containers'
    );

    // 5. Clean up old images
    await executeCommand('docker image prune -f', 'Cleaning up old images');

    // 6. Health check
    setTimeout(async () => {
      try {
        await executeCommand(
          'docker-compose ps',
          'Checking container status'
        );
        console.log('✅ Deployment completed successfully!');
        
        // Log deployment
        const logEntry = `${timestamp} - Deployment successful\n`;
        fs.appendFileSync('deploy.log', logEntry);
      } catch (error) {
        console.error('❌ Health check failed:', error);
      }
    }, 10000);

  } catch (error) {
    console.error('❌ Deployment failed:', error);
    
    // Log error
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} - Deployment failed: ${error.message}\n`;
    fs.appendFileSync('deploy.log', logEntry);
    
    throw error;
  }
}

// Endpoint do webhook
app.post('/webhook', verifySignature, async (req, res) => {
  const { ref, repository, commits } = req.body;
  
  // Verificar se é push na branch correta
  if (ref !== `refs/heads/${DEPLOY_BRANCH}`) {
    console.log(`ℹ️  Ignoring push to ${ref} (not ${DEPLOY_BRANCH})`);
    return res.status(200).send('OK - Branch ignored');
  }

  // Verificar se há commits
  if (!commits || commits.length === 0) {
    console.log('ℹ️  No commits found, skipping deployment');
    return res.status(200).send('OK - No commits');
  }

  console.log(`📦 Received push to ${repository.name}:${DEPLOY_BRANCH}`);
  console.log(`📝 Commits: ${commits.length}`);
  
  commits.forEach(commit => {
    console.log(`  - ${commit.id.substring(0, 7)}: ${commit.message}`);
  });

  res.status(200).send('OK - Deployment triggered');

  // Executar deploy em background
  deployApplication().catch(error => {
    console.error('❌ Deployment process failed:', error);
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Status endpoint
app.get('/status', (req, res) => {
  try {
    const logPath = 'deploy.log';
    const logs = fs.existsSync(logPath) 
      ? fs.readFileSync(logPath, 'utf8').split('\n').slice(-10).join('\n')
      : 'No deployment logs found';
    
    res.json({
      status: 'OK',
      lastLogs: logs,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log(`🎯 Webhook listener running on port ${PORT}`);
  console.log(`🔧 Watching for pushes to branch: ${DEPLOY_BRANCH}`);
  console.log(`🔐 Webhook secret configured: ${SECRET ? 'Yes' : 'No'}`);
  console.log(`📍 Endpoints:`);
  console.log(`   POST /webhook - GitHub webhook`);
  console.log(`   GET  /health  - Health check`);
  console.log(`   GET  /status  - Deployment status`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 Received SIGINT, shutting down gracefully');
  process.exit(0);
});
