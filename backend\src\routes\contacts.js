const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const multer = require('multer');
const cloudinary = require('../config/cloudinary');

const prisma = new PrismaClient();

// Configurar multer para upload de arquivos
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB
});

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticateToken);

// Listar todos os contatos
router.get('/', async (req, res) => {
  try {
    const userId = req.user.id;

    const contacts = await prisma.contact.findMany({
      where: { userId },
      include: {
        loans: {
          where: { status: 'ACTIVE' },
          select: {
            id: true,
            type: true,
            totalAmount: true,
            paidInstallments: true,
            installments: true
          }
        }
      },
      orderBy: { name: 'asc' }
    });

    res.json(contacts);
  } catch (error) {
    console.error('Erro ao buscar contatos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar contato por ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const contact = await prisma.contact.findFirst({
      where: { id, userId },
      include: {
        loans: {
          include: {
            bank: true,
            payments: {
              include: {
                bank: true
              },
              orderBy: { dueDate: 'asc' }
            }
          },
          orderBy: { createdAt: 'desc' }
        }
      }
    });

    if (!contact) {
      return res.status(404).json({ error: 'Contato não encontrado' });
    }

    res.json(contact);
  } catch (error) {
    console.error('Erro ao buscar contato:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo contato
router.post('/', upload.single('photo'), async (req, res) => {
  try {
    const userId = req.user.id;
    const { name, email, phone, notes } = req.body;

    let photoUrl = null;

    // Upload da foto se fornecida
    if (req.file) {
      try {
        const result = await new Promise((resolve, reject) => {
          cloudinary.uploader.upload_stream(
            {
              resource_type: 'image',
              folder: 'contacts',
              transformation: [
                { width: 300, height: 300, crop: 'fill', gravity: 'face' }
              ]
            },
            (error, result) => {
              if (error) reject(error);
              else resolve(result);
            }
          ).end(req.file.buffer);
        });

        photoUrl = result.secure_url;
      } catch (uploadError) {
        console.error('Erro no upload da foto:', uploadError);
        // Continuar sem a foto se houver erro
      }
    }

    const contact = await prisma.contact.create({
      data: {
        name,
        email: email || null,
        phone: phone || null,
        photoUrl,
        notes: notes || null,
        userId
      }
    });

    res.status(201).json(contact);
  } catch (error) {
    console.error('Erro ao criar contato:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar contato
router.put('/:id', upload.single('photo'), async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { name, email, phone, notes } = req.body;

    // Verificar se o contato pertence ao usuário
    const existingContact = await prisma.contact.findFirst({
      where: { id, userId }
    });

    if (!existingContact) {
      return res.status(404).json({ error: 'Contato não encontrado' });
    }

    let photoUrl = existingContact.photoUrl;

    // Upload da nova foto se fornecida
    if (req.file) {
      try {
        const result = await new Promise((resolve, reject) => {
          cloudinary.uploader.upload_stream(
            {
              resource_type: 'image',
              folder: 'contacts',
              transformation: [
                { width: 300, height: 300, crop: 'fill', gravity: 'face' }
              ]
            },
            (error, result) => {
              if (error) reject(error);
              else resolve(result);
            }
          ).end(req.file.buffer);
        });

        photoUrl = result.secure_url;
      } catch (uploadError) {
        console.error('Erro no upload da foto:', uploadError);
        // Manter foto anterior se houver erro
      }
    }

    const contact = await prisma.contact.update({
      where: { id },
      data: {
        name,
        email: email || null,
        phone: phone || null,
        photoUrl,
        notes: notes || null
      }
    });

    res.json(contact);
  } catch (error) {
    console.error('Erro ao atualizar contato:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar contato
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Verificar se o contato pertence ao usuário
    const existingContact = await prisma.contact.findFirst({
      where: { id, userId }
    });

    if (!existingContact) {
      return res.status(404).json({ error: 'Contato não encontrado' });
    }

    // Buscar todos os empréstimos do contato
    const loans = await prisma.loan.findMany({
      where: { contactId: id },
      include: {
        payments: true,
        bank: true
      }
    });

    // Deletar transações relacionadas aos empréstimos
    for (const loan of loans) {
      await prisma.transaction.deleteMany({
        where: {
          userId,
          OR: [
            { description: { contains: loan.title } },
            { description: { startsWith: `Empréstimo para ${loan.title}` } },
            { description: { startsWith: `Empréstimo de ${loan.title}` } },
            { description: { startsWith: `Pagamento: ${loan.title}` } }
          ]
        }
      });

      // Reverter saldos dos bancos se necessário
      if (loan.bankId && loan.bank) {
        const paidAmount = loan.payments
          .filter(p => p.isPaid)
          .reduce((sum, p) => sum + p.amount, 0);

        const initialReversion = loan.type === 'LOAN_GIVEN' ?
          loan.bank.currentBalance + loan.totalAmount :
          loan.bank.currentBalance - loan.totalAmount;

        const finalBalance = loan.type === 'LOAN_GIVEN' ?
          initialReversion - paidAmount :
          initialReversion + paidAmount;

        await prisma.bank.update({
          where: { id: loan.bankId },
          data: { currentBalance: finalBalance }
        });
      }
    }

    // Deletar parcelas de pagamento
    await prisma.loanPayment.deleteMany({
      where: {
        loanId: {
          in: loans.map(loan => loan.id)
        }
      }
    });

    // Deletar empréstimos
    await prisma.loan.deleteMany({
      where: { contactId: id }
    });

    // Deletar contato
    await prisma.contact.delete({
      where: { id }
    });

    res.json({ message: 'Contato deletado com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar contato:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar status do contato manualmente
router.patch('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    const userId = req.user.id;

    // Verificar se o contato pertence ao usuário
    const existingContact = await prisma.contact.findFirst({
      where: { id, userId }
    });

    if (!existingContact) {
      return res.status(404).json({ error: 'Contato não encontrado' });
    }

    // Validar status
    const validStatuses = ['GOOD', 'NEUTRAL', 'BAD'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: 'Status inválido' });
    }

    const contact = await prisma.contact.update({
      where: { id },
      data: { status }
    });

    res.json(contact);
  } catch (error) {
    console.error('Erro ao atualizar status do contato:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar status do contato (chamado automaticamente)
async function updateContactStatus(contactId) {
  try {
    const contact = await prisma.contact.findUnique({
      where: { id: contactId },
      include: {
        loans: {
          include: {
            payments: true
          }
        }
      }
    });

    if (!contact) return;

    let totalPayments = 0;
    let onTimePayments = 0;
    let latePayments = 0;

    contact.loans.forEach(loan => {
      loan.payments.forEach(payment => {
        if (payment.isPaid) {
          totalPayments++;
          if (payment.isLate) {
            latePayments++;
          } else {
            onTimePayments++;
          }
        }
      });
    });

    // Calcular status baseado na performance
    let status = 'NEUTRAL';
    if (totalPayments > 0) {
      const onTimePercentage = (onTimePayments / totalPayments) * 100;
      if (onTimePercentage >= 80) {
        status = 'GOOD';
      } else if (onTimePercentage < 50) {
        status = 'BAD';
      }
    }

    await prisma.contact.update({
      where: { id: contactId },
      data: {
        status,
        totalLoans: contact.loans.length,
        paidOnTime: onTimePayments,
        latePayments
      }
    });
  } catch (error) {
    console.error('Erro ao atualizar status do contato:', error);
  }
}

module.exports = { router, updateContactStatus };
