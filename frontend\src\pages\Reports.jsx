import React, { useState, useEffect } from 'react'
import { 
  FileText, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  Target, 
  PiggyBank,
  Lightbulb,
  DollarSign,
  Calendar,
  BarChart3,
  PieChart,
  ArrowRight,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react'
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  BarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell
} from 'recharts'
import api from '../services/api'
import toast from 'react-hot-toast'

function Reports({ selectedYear }) {
  const [reportData, setReportData] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchReportData()
  }, [selectedYear])

  const fetchReportData = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/dashboard?year=${selectedYear || new Date().getFullYear()}`)
      setReportData(response.data)
    } catch (error) {
      console.error('Erro ao buscar dados do relatório:', error)
      toast.error('Erro ao carregar dados do relatório')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`
  }

  // Análises inteligentes baseadas nos dados
  const getFinancialAnalysis = () => {
    if (!reportData) return {}

    const { summary } = reportData
    const savingsRate = summary.savingsRate || 0
    const expenseRatio = summary.expenseRatio || 0
    const liquidBalance = summary.liquidBalance || 0
    
    return {
      savingsHealth: savingsRate >= 20 ? 'excellent' : savingsRate >= 10 ? 'good' : savingsRate >= 5 ? 'warning' : 'critical',
      spendingHealth: expenseRatio <= 70 ? 'excellent' : expenseRatio <= 80 ? 'good' : expenseRatio <= 90 ? 'warning' : 'critical',
      balanceHealth: liquidBalance > 0 ? 'positive' : 'negative'
    }
  }

  const getRecommendations = () => {
    if (!reportData) return []

    const { summary } = reportData
    const analysis = getFinancialAnalysis()
    const recommendations = []

    // Recomendações baseadas na taxa de economia
    if (analysis.savingsHealth === 'critical') {
      recommendations.push({
        type: 'critical',
        title: 'Emergência: Taxa de Economia Muito Baixa',
        description: 'Sua taxa de economia está abaixo de 5%. É urgente revisar seus gastos.',
        action: 'Corte gastos desnecessários imediatamente',
        icon: AlertTriangle,
        color: 'red'
      })
    } else if (analysis.savingsHealth === 'warning') {
      recommendations.push({
        type: 'warning',
        title: 'Melhore sua Taxa de Economia',
        description: 'Tente economizar pelo menos 10% da sua receita mensal.',
        action: 'Identifique gastos que podem ser reduzidos',
        icon: Target,
        color: 'yellow'
      })
    }

    // Recomendações baseadas nos gastos
    if (analysis.spendingHealth === 'critical') {
      recommendations.push({
        type: 'critical',
        title: 'Gastos Excessivos Detectados',
        description: 'Você está gastando mais de 90% da sua receita.',
        action: 'Revise urgentemente suas categorias de maior gasto',
        icon: TrendingDown,
        color: 'red'
      })
    }

    // Recomendações positivas
    if (analysis.savingsHealth === 'excellent') {
      recommendations.push({
        type: 'success',
        title: 'Excelente Controle Financeiro!',
        description: 'Sua taxa de economia está acima de 20%. Continue assim!',
        action: 'Considere investir o excedente',
        icon: CheckCircle,
        color: 'green'
      })
    }

    // Recomendações específicas por categoria
    const topCategories = reportData.charts.categoryData?.slice(0, 3) || []
    if (topCategories.length > 0) {
      recommendations.push({
        type: 'info',
        title: `Categoria de Maior Gasto: ${topCategories[0]?.name}`,
        description: `Você gastou ${formatCurrency(topCategories[0]?.value)} nesta categoria.`,
        action: 'Analise se há oportunidades de economia nesta área',
        icon: PieChart,
        color: 'blue'
      })
    }

    return recommendations
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!reportData) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Erro ao carregar dados do relatório</p>
      </div>
    )
  }

  const { summary, charts } = reportData
  const analysis = getFinancialAnalysis()
  const recommendations = getRecommendations()

  return (
    <div className="space-y-8 fade-in">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl p-6 border border-indigo-100">
        <div className="flex items-center mb-4">
          <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mr-4">
            <FileText className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Relatórios Inteligentes</h1>
            <p className="text-sm text-gray-600">Análises e recomendações para otimizar suas finanças em {selectedYear || new Date().getFullYear()}</p>
          </div>
        </div>
      </div>

      {/* Resumo Executivo */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <BarChart3 className="h-5 w-5 mr-2 text-indigo-600" />
          Resumo Executivo
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Saúde da Economia */}
          <div className={`p-4 rounded-lg border-l-4 ${
            analysis.savingsHealth === 'excellent' ? 'border-green-500 bg-green-50' :
            analysis.savingsHealth === 'good' ? 'border-blue-500 bg-blue-50' :
            analysis.savingsHealth === 'warning' ? 'border-yellow-500 bg-yellow-50' :
            'border-red-500 bg-red-50'
          }`}>
            <div className="flex items-center mb-2">
              <PiggyBank className={`h-5 w-5 mr-2 ${
                analysis.savingsHealth === 'excellent' ? 'text-green-600' :
                analysis.savingsHealth === 'good' ? 'text-blue-600' :
                analysis.savingsHealth === 'warning' ? 'text-yellow-600' :
                'text-red-600'
              }`} />
              <h3 className="font-semibold text-gray-900">Taxa de Economia</h3>
            </div>
            <p className="text-2xl font-bold text-gray-900">{formatPercentage(summary.savingsRate || 0)}</p>
            <p className="text-sm text-gray-600">
              {analysis.savingsHealth === 'excellent' ? 'Excelente!' :
               analysis.savingsHealth === 'good' ? 'Boa' :
               analysis.savingsHealth === 'warning' ? 'Precisa melhorar' :
               'Crítica'}
            </p>
          </div>

          {/* Controle de Gastos */}
          <div className={`p-4 rounded-lg border-l-4 ${
            analysis.spendingHealth === 'excellent' ? 'border-green-500 bg-green-50' :
            analysis.spendingHealth === 'good' ? 'border-blue-500 bg-blue-50' :
            analysis.spendingHealth === 'warning' ? 'border-yellow-500 bg-yellow-50' :
            'border-red-500 bg-red-50'
          }`}>
            <div className="flex items-center mb-2">
              <TrendingDown className={`h-5 w-5 mr-2 ${
                analysis.spendingHealth === 'excellent' ? 'text-green-600' :
                analysis.spendingHealth === 'good' ? 'text-blue-600' :
                analysis.spendingHealth === 'warning' ? 'text-yellow-600' :
                'text-red-600'
              }`} />
              <h3 className="font-semibold text-gray-900">Taxa de Gastos</h3>
            </div>
            <p className="text-2xl font-bold text-gray-900">{formatPercentage(summary.expenseRatio || 0)}</p>
            <p className="text-sm text-gray-600">
              {analysis.spendingHealth === 'excellent' ? 'Controlada' :
               analysis.spendingHealth === 'good' ? 'Boa' :
               analysis.spendingHealth === 'warning' ? 'Atenção' :
               'Excessiva'}
            </p>
          </div>

          {/* Saldo Líquido */}
          <div className={`p-4 rounded-lg border-l-4 ${
            analysis.balanceHealth === 'positive' ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'
          }`}>
            <div className="flex items-center mb-2">
              <DollarSign className={`h-5 w-5 mr-2 ${
                analysis.balanceHealth === 'positive' ? 'text-green-600' : 'text-red-600'
              }`} />
              <h3 className="font-semibold text-gray-900">Saldo Líquido</h3>
            </div>
            <p className="text-2xl font-bold text-gray-900">{formatCurrency(summary.liquidBalance || 0)}</p>
            <p className="text-sm text-gray-600">
              {analysis.balanceHealth === 'positive' ? 'Positivo' : 'Negativo'}
            </p>
          </div>
        </div>
      </div>

      {/* Recomendações Inteligentes */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <Lightbulb className="h-5 w-5 mr-2 text-yellow-600" />
          Recomendações Inteligentes
        </h2>

        <div className="space-y-4">
          {recommendations.map((rec, index) => (
            <div key={index} className={`p-4 rounded-lg border-l-4 ${
              rec.color === 'red' ? 'border-red-500 bg-red-50' :
              rec.color === 'yellow' ? 'border-yellow-500 bg-yellow-50' :
              rec.color === 'green' ? 'border-green-500 bg-green-50' :
              'border-blue-500 bg-blue-50'
            }`}>
              <div className="flex items-start">
                <rec.icon className={`h-5 w-5 mr-3 mt-0.5 ${
                  rec.color === 'red' ? 'text-red-600' :
                  rec.color === 'yellow' ? 'text-yellow-600' :
                  rec.color === 'green' ? 'text-green-600' :
                  'text-blue-600'
                }`} />
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-1">{rec.title}</h3>
                  <p className="text-gray-700 text-sm mb-2">{rec.description}</p>
                  <div className="flex items-center text-sm">
                    <ArrowRight className="h-4 w-4 mr-1 text-gray-500" />
                    <span className="font-medium text-gray-900">{rec.action}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Análise de Gastos por Categoria */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <PieChart className="h-5 w-5 mr-2 text-purple-600" />
          Para Onde Vai Seu Dinheiro
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Gráfico de Pizza */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Distribuição de Gastos</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsPieChart>
                  <Pie
                    data={charts.categoryData || []}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={2}
                    dataKey="value"
                    label={({ percent }) => percent > 0.05 ? `${(percent * 100).toFixed(1)}%` : ''}
                    labelLine={false}
                  >
                    {(charts.categoryData || []).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) => [formatCurrency(value), 'Gasto']}
                    contentStyle={{
                      backgroundColor: 'white',
                      border: '1px solid #E5E7EB',
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                    }}
                  />
                </RechartsPieChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Top Categorias */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Maiores Gastos</h3>
            <div className="space-y-3">
              {(charts.categoryData || []).slice(0, 5).map((category, index) => {
                const percentage = (category.value / summary.totalExpenses) * 100
                return (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center">
                      <div
                        className="w-4 h-4 rounded-full mr-3"
                        style={{ backgroundColor: category.color }}
                      ></div>
                      <div>
                        <p className="font-medium text-gray-900">{category.icon} {category.name}</p>
                        <p className="text-sm text-gray-600">{formatPercentage(percentage)} do total</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-gray-900">{formatCurrency(category.value)}</p>
                      <p className="text-sm text-gray-600">#{index + 1}</p>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Estratégias de Economia */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <Target className="h-5 w-5 mr-2 text-green-600" />
          Como Economizar Mais no Próximo Mês
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                <TrendingDown className="h-4 w-4 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900">Reduza 10% dos Gastos</h3>
            </div>
            <p className="text-sm text-gray-600 mb-2">
              Identifique gastos desnecessários na sua maior categoria de despesa.
            </p>
            <p className="text-lg font-bold text-green-600">
              Economia: {formatCurrency((summary.totalExpenses * 0.1) || 0)}
            </p>
          </div>

          <div className="bg-white rounded-lg p-4 shadow-sm">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <Calendar className="h-4 w-4 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900">Planeje Compras</h3>
            </div>
            <p className="text-sm text-gray-600 mb-2">
              Faça uma lista antes de comprar e evite compras por impulso.
            </p>
            <p className="text-lg font-bold text-blue-600">
              Economia: {formatCurrency((summary.totalExpenses * 0.05) || 0)}
            </p>
          </div>

          <div className="bg-white rounded-lg p-4 shadow-sm">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                <PiggyBank className="h-4 w-4 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900">Meta de Economia</h3>
            </div>
            <p className="text-sm text-gray-600 mb-2">
              Estabeleça uma meta de economizar 20% da sua receita mensal.
            </p>
            <p className="text-lg font-bold text-purple-600">
              Meta: {formatCurrency((summary.avgMonthlyIncome * 0.2) || 0)}
            </p>
          </div>
        </div>
      </div>

      {/* Projeções Futuras */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <TrendingUp className="h-5 w-5 mr-2 text-indigo-600" />
          Projeções para o Próximo Ano
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-2">Se Mantiver o Padrão Atual</h3>
            <p className="text-2xl font-bold text-blue-600 mb-1">
              {formatCurrency((summary.liquidBalance || 0) * 1.1)}
            </p>
            <p className="text-sm text-gray-600">Saldo projetado</p>
          </div>

          <div className="text-center p-4 bg-green-50 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-2">Se Economizar 10% Mais</h3>
            <p className="text-2xl font-bold text-green-600 mb-1">
              {formatCurrency(((summary.liquidBalance || 0) + (summary.totalExpenses * 0.1)) * 1.1)}
            </p>
            <p className="text-sm text-gray-600">Saldo projetado</p>
          </div>

          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-2">Se Investir o Excedente</h3>
            <p className="text-2xl font-bold text-purple-600 mb-1">
              {formatCurrency(((summary.liquidBalance || 0) * 1.15))}
            </p>
            <p className="text-sm text-gray-600">Com 15% de rendimento</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Reports
