import React, { useState } from 'react'
import { Search, X } from 'lucide-react'

// Lista extensa de ícones organizados por categoria
const iconCategories = {
  'Finanças': [
    '💰', '💵', '💴', '💶', '💷', '💳', '🏦', '💎', '📊', '📈', '📉', '💹', '🪙', '💸', '🧾', '💰'
  ],
  'Alimentação': [
    '🍕', '🍔', '🍟', '🌭', '🥪', '🌮', '🌯', '🥙', '🍝', '🍜', '🍲', '🍱', '🍣', '🍤', '🍙', '🍘',
    '🍚', '🍛', '🍖', '🍗', '🥓', '🍳', '🥞', '🧇', '🥐', '🍞', '🥖', '🥨', '🧀', '🥯', '🍎', '🍊',
    '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑',
    '🥦', '🥬', '🥒', '🌶️', '🫑', '🌽', '🥕', '🫒', '🧄', '🧅', '🥔', '🍠', '🥐', '☕', '🍵', '🧃',
    '🥤', '🧋', '🍶', '🍾', '🍷', '🍸', '🍹', '🍺', '🍻', '🥂', '🥃', '🍽️', '🍴', '🥄', '🔪', '🏺'
  ],
  'Transporte': [
    '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐', '🛻', '🚚', '🚛', '🚜', '🏍️', '🛵',
    '🚲', '🛴', '🛹', '🛼', '🚁', '✈️', '🛩️', '🛫', '🛬', '🪂', '💺', '🚀', '🛸', '🚊', '🚝', '🚞',
    '🚋', '🚃', '🚂', '🚆', '🚄', '🚅', '🚈', '🚇', '🚉', '🚐', '⛽', '🅿️', '🚥', '🚦', '🛣️', '🛤️'
  ],
  'Casa & Moradia': [
    '🏠', '🏡', '🏘️', '🏚️', '🏗️', '🏭', '🏢', '🏬', '🏣', '🏤', '🏥', '🏦', '🏨', '🏪', '🏫', '🏩',
    '💒', '🏛️', '⛪', '🕌', '🛕', '🕍', '⛩️', '🕋', '🛏️', '🛋️', '🪑', '🚿', '🛁', '🚽', '🪠', '🧴',
    '🧽', '🧹', '🧺', '🔌', '💡', '🕯️', '🪔', '🔦', '🏮', '🪆', '🧸', '🖼️', '🪟', '🚪', '🪜', '🪣'
  ],
  'Saúde & Beleza': [
    '💊', '💉', '🩹', '🩺', '🌡️', '🧴', '🧼', '🧽', '🪥', '🪒', '💄', '💅', '💆', '💇', '🧖', '🧘',
    '🏃', '🚶', '🧍', '🤸', '🏋️', '🤾', '🏌️', '🏇', '🧗', '🏊', '🚴', '🤽', '🏄', '🏂', '⛷️', '🤿'
  ],
  'Educação & Trabalho': [
    '📚', '📖', '📝', '✏️', '✒️', '🖊️', '🖋️', '🖍️', '📄', '📃', '📑', '📊', '📈', '📉', '📋', '📌',
    '📍', '📎', '🖇️', '📏', '📐', '✂️', '🗃️', '🗂️', '🗞️', '📰', '📓', '📔', '📒', '📕', '📗', '📘',
    '📙', '📜', '🎓', '🖥️', '💻', '⌨️', '🖱️', '🖨️', '📱', '☎️', '📞', '📟', '📠', '📺', '📻', '🎙️'
  ],
  'Entretenimento': [
    '🎮', '🕹️', '🎲', '🎯', '🎳', '🎪', '🎭', '🎨', '🎬', '🎤', '🎧', '🎼', '🎵', '🎶', '🎹', '🥁',
    '🎷', '🎺', '🎸', '🪕', '🎻', '🎪', '🎡', '🎢', '🎠', '🎟️', '🎫', '🎖️', '🏆', '🥇', '🥈', '🥉',
    '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱', '🪀', '🏓', '🏸', '🏒', '🏑', '🥍'
  ],
  'Natureza': [
    '🌱', '🌿', '☘️', '🍀', '🎋', '🍃', '🌾', '🌵', '🌲', '🌳', '🌴', '🌸', '🌺', '🌻', '🌹', '🥀',
    '🌷', '💐', '🌼', '🌙', '🌛', '🌜', '🌚', '🌕', '🌖', '🌗', '🌘', '🌑', '🌒', '🌓', '🌔', '🌝',
    '🌞', '⭐', '🌟', '💫', '✨', '☄️', '☀️', '🌤️', '⛅', '🌥️', '☁️', '🌦️', '🌧️', '⛈️', '🌩️', '🌨️'
  ],
  'Objetos': [
    '⌚', '📱', '📲', '💻', '⌨️', '🖥️', '🖨️', '🖱️', '🖲️', '🕹️', '🗜️', '💽', '💾', '💿', '📀', '📼',
    '📷', '📸', '📹', '🎥', '📽️', '🎞️', '📞', '☎️', '📟', '📠', '📺', '📻', '🎙️', '🎚️', '🎛️', '🧭',
    '⏱️', '⏲️', '⏰', '🕰️', '⌛', '⏳', '📡', '🔋', '🔌', '💡', '🔦', '🕯️', '🪔', '🧯', '🛢️', '💸'
  ]
}

function IconSelector({ selectedIcon, onIconSelect, onClose }) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('Finanças')

  // Filtrar ícones baseado na busca
  const getFilteredIcons = () => {
    if (!searchTerm) {
      return iconCategories[selectedCategory] || []
    }
    
    // Buscar em todas as categorias se houver termo de busca
    const allIcons = Object.values(iconCategories).flat()
    return allIcons.filter(icon => {
      // Aqui você pode implementar uma lógica mais sofisticada de busca
      // Por enquanto, vamos apenas retornar todos os ícones quando há busca
      return true
    })
  }

  const filteredIcons = getFilteredIcons()

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white p-6">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-bold">Selecionar Ícone</h3>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          
          {/* Busca */}
          <div className="mt-4 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-purple-200" />
            <input
              type="text"
              placeholder="Buscar ícones..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-xl placeholder-purple-200 text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
            />
          </div>
        </div>

        <div className="flex h-96">
          {/* Categorias */}
          <div className="w-1/3 bg-gray-50 border-r border-gray-200 overflow-y-auto">
            <div className="p-4">
              <h4 className="text-sm font-semibold text-gray-700 mb-3">Categorias</h4>
              {Object.keys(iconCategories).map((category) => (
                <button
                  key={category}
                  onClick={() => {
                    setSelectedCategory(category)
                    setSearchTerm('')
                  }}
                  className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors mb-1 ${
                    selectedCategory === category
                      ? 'bg-purple-100 text-purple-700 font-medium'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* Ícones */}
          <div className="flex-1 p-4 overflow-y-auto">
            <div className="grid grid-cols-8 gap-2">
              {filteredIcons.map((icon, index) => (
                <button
                  key={index}
                  onClick={() => onIconSelect(icon)}
                  className={`w-12 h-12 rounded-lg border-2 flex items-center justify-center text-2xl hover:bg-purple-50 hover:border-purple-300 transition-all ${
                    selectedIcon === icon
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200'
                  }`}
                >
                  {icon}
                </button>
              ))}
            </div>
            
            {filteredIcons.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <p>Nenhum ícone encontrado</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-600">
              Ícone selecionado: <span className="text-2xl ml-2">{selectedIcon || '❓'}</span>
            </p>
            <div className="flex gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={onClose}
                className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                Confirmar
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default IconSelector
