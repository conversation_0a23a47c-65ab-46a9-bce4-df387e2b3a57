@echo off
echo ========================================
echo    🌙 Sara - Sistema de Gastos
echo    Iniciando Sistema...
echo ========================================
echo.

echo ⏳ Verificando Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js nao encontrado!
    echo Execute install.bat primeiro
    pause
    exit /b 1
)

echo ✅ Iniciando backend e frontend...
echo.
echo 🌐 O sistema sera aberto em:
echo    Frontend: http://localhost:5173
echo    Backend:  http://localhost:3001
echo.
echo 👤 Credenciais de teste:
echo    Email: <EMAIL>
echo    Senha: 123456
echo.
echo ⚠️  Para parar o sistema, pressione Ctrl+C
echo.

npm run dev
