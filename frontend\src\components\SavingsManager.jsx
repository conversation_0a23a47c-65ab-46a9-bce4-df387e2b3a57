import React, { useState, useEffect } from 'react'
import { Plus, Edit3, Trash2, PiggyBank, Target, Lock, Unlock, ArrowUp, ArrowDown } from 'lucide-react'
import { savingsService, bankService } from '../services/bankService'
import CurrencyInput from './CurrencyInput'
import toast from 'react-hot-toast'

function SavingsManager() {
  const [savings, setSavings] = useState([])
  const [banks, setBanks] = useState([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showDepositModal, setShowDepositModal] = useState(false)
  const [showWithdrawModal, setShowWithdrawModal] = useState(false)
  const [editingSavings, setEditingSavings] = useState(null)
  const [selectedSavings, setSelectedSavings] = useState(null)
  const [selectedBankId, setSelectedBankId] = useState('')
  const [transactionAmount, setTransactionAmount] = useState(0)

  const [newSavings, setNewSavings] = useState({
    name: '',
    description: '',
    targetAmount: 0,
    icon: '🐷',
    color: '#22C55E',
    bankId: ''
  })

  const savingsIcons = ['🐷', '💰', '🎯', '🏠', '🚗', '✈️', '🎓', '💍']
  const savingsColors = [
    '#22C55E', '#3B82F6', '#F59E0B', '#EF4444',
    '#8B5CF6', '#EC4899', '#14B8A6', '#6B7280'
  ]

  useEffect(() => {
    fetchSavings()
    fetchBanks()
  }, [])

  const fetchSavings = async () => {
    try {
      const data = await savingsService.getSavings()
      setSavings(data)
    } catch (error) {
      toast.error('Erro ao carregar cofrinhos')
    } finally {
      setLoading(false)
    }
  }

  const fetchBanks = async () => {
    try {
      const data = await bankService.getBanks()
      setBanks(data)
    } catch (error) {
      console.error('Erro ao carregar bancos:', error)
    }
  }

  const handleCreateSavings = async () => {
    try {
      if (!newSavings.name || !newSavings.bankId) {
        toast.error('Nome e banco são obrigatórios')
        return
      }

      await savingsService.createSavings(newSavings)
      toast.success('Cofrinho criado com sucesso!')
      setShowAddModal(false)
      setNewSavings({ name: '', description: '', targetAmount: 0, icon: '🐷', color: '#22C55E', bankId: '' })
      fetchSavings()
      fetchBanks()
    } catch (error) {
      toast.error('Erro ao criar cofrinho')
    }
  }

  const handleUpdateSavings = async () => {
    try {
      await savingsService.updateSavings(editingSavings.id, editingSavings)
      toast.success('Cofrinho atualizado com sucesso!')
      setShowEditModal(false)
      setEditingSavings(null)
      fetchSavings()
    } catch (error) {
      toast.error('Erro ao atualizar cofrinho')
    }
  }

  const handleDeleteSavings = async (savingsItem) => {
    if (!confirm(`Tem certeza que deseja excluir o cofrinho "${savingsItem.name}"?`)) {
      return
    }

    try {
      await savingsService.deleteSavings(savingsItem.id)
      toast.success('Cofrinho excluído com sucesso!')
      fetchSavings()
      fetchBanks()
    } catch (error) {
      toast.error(error.response?.data?.error || 'Erro ao excluir cofrinho')
    }
  }

  const handleDeposit = async () => {
    try {
      if (transactionAmount <= 0) {
        toast.error('Valor deve ser maior que zero')
        return
      }

      if (!selectedBankId) {
        toast.error('Selecione um banco')
        return
      }

      await savingsService.deposit(selectedSavings.id, transactionAmount, selectedBankId)
      toast.success('Depósito realizado com sucesso!')
      setShowDepositModal(false)
      setSelectedSavings(null)
      setSelectedBankId('')
      setTransactionAmount(0)
      fetchSavings()
      fetchBanks()
    } catch (error) {
      toast.error(error.response?.data?.error || 'Erro ao realizar depósito')
    }
  }

  const handleWithdraw = async () => {
    try {
      if (transactionAmount <= 0) {
        toast.error('Valor deve ser maior que zero')
        return
      }

      if (!selectedBankId) {
        toast.error('Selecione um banco')
        return
      }

      await savingsService.withdraw(selectedSavings.id, transactionAmount, selectedBankId)
      toast.success('Saque realizado com sucesso!')
      setShowWithdrawModal(false)
      setSelectedSavings(null)
      setSelectedBankId('')
      setTransactionAmount(0)
      fetchSavings()
      fetchBanks()
    } catch (error) {
      toast.error(error.response?.data?.error || 'Erro ao realizar saque')
    }
  }

  const toggleLock = async (savingsItem) => {
    try {
      await savingsService.updateSavings(savingsItem.id, { isLocked: !savingsItem.isLocked })
      fetchSavings()
      toast.success(`Cofrinho ${!savingsItem.isLocked ? 'bloqueado' : 'desbloqueado'} com sucesso!`)
    } catch (error) {
      toast.error('Erro ao alterar status do cofrinho')
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const getProgress = (current, target) => {
    if (target === 0) return 0
    return Math.min((current / target) * 100, 100)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Cofrinhos</h1>
          <p className="text-gray-600 mt-1">Organize suas economias e metas financeiras</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-slate-900 rounded-lg hover:bg-slate-800 transition-colors"
        >
          <Plus className="h-4 w-4" />
          <span>Novo Cofrinho</span>
        </button>
      </div>

      {/* Lista de Cofrinhos */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {savings.map((savingsItem) => (
          <div
            key={savingsItem.id}
            className="bg-white rounded-2xl border shadow-sm p-6 transition-all hover:shadow-md"
          >
            <div className="flex items-start justify-between mb-6">
              <div className="flex items-center gap-4">
                <div
                  className="w-14 h-14 rounded-2xl flex items-center justify-center text-2xl shadow-sm"
                  style={{ backgroundColor: savingsItem.color + '15', color: savingsItem.color, border: `2px solid ${savingsItem.color}20` }}
                >
                  {savingsItem.icon}
                </div>
                <div>
                  <h3 className="font-bold text-gray-900 text-lg">{savingsItem.name}</h3>
                  <div className="flex items-center gap-2 mt-1">
                    <div className={`w-2 h-2 rounded-full ${savingsItem.isLocked ? 'bg-red-500' : 'bg-green-500'}`} />
                    <p className="text-sm text-gray-500">
                      {savingsItem.bank.icon} {savingsItem.bank.name}
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <button
                  onClick={() => toggleLock(savingsItem)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  title={savingsItem.isLocked ? 'Desbloquear' : 'Bloquear'}
                >
                  {savingsItem.isLocked ? <Lock className="h-4 w-4 text-red-600" /> : <Unlock className="h-4 w-4 text-green-600" />}
                </button>
                <button
                  onClick={() => {
                    setEditingSavings(savingsItem)
                    setShowEditModal(true)
                  }}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Editar"
                >
                  <Edit3 className="h-4 w-4 text-gray-600" />
                </button>
                <button
                  onClick={() => handleDeleteSavings(savingsItem)}
                  className="p-2 hover:bg-red-50 rounded-lg text-red-600 transition-colors"
                  title="Excluir"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Progresso da Meta */}
            {savingsItem.targetAmount > 0 && (
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-600">Meta</span>
                  <span className="text-sm text-gray-500">
                    {getProgress(savingsItem.currentAmount, savingsItem.targetAmount).toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="h-2 rounded-full transition-all"
                    style={{
                      width: `${getProgress(savingsItem.currentAmount, savingsItem.targetAmount)}%`,
                      backgroundColor: savingsItem.color
                    }}
                  />
                </div>
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>{formatCurrency(savingsItem.currentAmount)}</span>
                  <span>{formatCurrency(savingsItem.targetAmount)}</span>
                </div>
              </div>
            )}

            {/* Saldo Atual */}
            <div className="bg-gray-50 rounded-xl p-4 mb-4">
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-1">Saldo Atual</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(savingsItem.currentAmount)}
                </p>
              </div>
            </div>

            {/* Ações */}
            <div className="flex gap-2">
              <button
                onClick={() => {
                  setSelectedSavings(savingsItem)
                  setShowDepositModal(true)
                }}
                className="flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium text-green-700 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
              >
                <ArrowUp className="h-4 w-4" />
                <span>Depositar</span>
              </button>
              <button
                onClick={() => {
                  setSelectedSavings(savingsItem)
                  setShowWithdrawModal(true)
                }}
                disabled={savingsItem.isLocked || savingsItem.currentAmount === 0}
                className="flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium text-red-700 bg-red-50 rounded-lg hover:bg-red-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ArrowDown className="h-4 w-4" />
                <span>Sacar</span>
              </button>
            </div>
          </div>
        ))}
      </div>

      {savings.length === 0 && (
        <div className="text-center py-12">
          <PiggyBank className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum cofrinho cadastrado</h3>
          <p className="text-gray-600 mb-4">Comece criando seu primeiro cofrinho para organizar suas economias</p>
          <button
            onClick={() => setShowAddModal(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Criar Primeiro Cofrinho</span>
          </button>
        </div>
      )}

      {/* Modal Adicionar Cofrinho */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Novo Cofrinho</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                <input
                  type="text"
                  value={newSavings.name}
                  onChange={(e) => setNewSavings({ ...newSavings, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: Viagem para Europa"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
                <textarea
                  value={newSavings.description}
                  onChange={(e) => setNewSavings({ ...newSavings, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Descrição opcional..."
                  rows="2"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Meta (Opcional)</label>
                <CurrencyInput
                  value={newSavings.targetAmount}
                  onChange={(value) => setNewSavings({ ...newSavings, targetAmount: value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="R$ 0,00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Banco</label>
                <select
                  value={newSavings.bankId}
                  onChange={(e) => setNewSavings({ ...newSavings, bankId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Selecione um banco</option>
                  {banks.map((bank) => (
                    <option key={bank.id} value={bank.id}>
                      {bank.icon} {bank.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ícone</label>
                <div className="flex flex-wrap gap-2">
                  {savingsIcons.map((icon) => (
                    <button
                      key={icon}
                      onClick={() => setNewSavings({ ...newSavings, icon })}
                      className={`w-10 h-10 rounded-lg border-2 flex items-center justify-center text-lg transition-colors ${
                        newSavings.icon === icon ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {icon}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Cor</label>
                <div className="flex flex-wrap gap-2">
                  {savingsColors.map((color) => (
                    <button
                      key={color}
                      onClick={() => setNewSavings({ ...newSavings, color })}
                      className={`w-8 h-8 rounded-full border-2 transition-all ${
                        newSavings.color === color ? 'border-gray-800 scale-110' : 'border-gray-300'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowAddModal(false)
                  setNewSavings({ name: '', description: '', targetAmount: 0, icon: '🐷', color: '#22C55E', bankId: '' })
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleCreateSavings}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Criar Cofrinho
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Editar Cofrinho */}
      {showEditModal && editingSavings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Editar Cofrinho</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                <input
                  type="text"
                  value={editingSavings.name}
                  onChange={(e) => setEditingSavings({ ...editingSavings, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: Viagem para Europa"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
                <textarea
                  value={editingSavings.description || ''}
                  onChange={(e) => setEditingSavings({ ...editingSavings, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Descrição opcional..."
                  rows="2"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Meta</label>
                <CurrencyInput
                  value={editingSavings.targetAmount}
                  onChange={(value) => setEditingSavings({ ...editingSavings, targetAmount: value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="R$ 0,00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ícone</label>
                <div className="flex flex-wrap gap-2">
                  {savingsIcons.map((icon) => (
                    <button
                      key={icon}
                      onClick={() => setEditingSavings({ ...editingSavings, icon })}
                      className={`w-10 h-10 rounded-lg border-2 flex items-center justify-center text-lg transition-colors ${
                        editingSavings.icon === icon ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {icon}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Cor</label>
                <div className="flex flex-wrap gap-2">
                  {savingsColors.map((color) => (
                    <button
                      key={color}
                      onClick={() => setEditingSavings({ ...editingSavings, color })}
                      className={`w-8 h-8 rounded-full border-2 transition-all ${
                        editingSavings.color === color ? 'border-gray-800 scale-110' : 'border-gray-300'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowEditModal(false)
                  setEditingSavings(null)
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleUpdateSavings}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Salvar Alterações
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Depositar */}
      {showDepositModal && selectedSavings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Depositar no Cofrinho</h3>
            <p className="text-gray-600 mb-4">{selectedSavings.icon} {selectedSavings.name}</p>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Valor</label>
                <CurrencyInput
                  value={transactionAmount}
                  onChange={setTransactionAmount}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="R$ 0,00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Banco</label>
                <select
                  value={selectedBankId}
                  onChange={(e) => setSelectedBankId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Selecione um banco</option>
                  {banks.map((bank) => (
                    <option key={bank.id} value={bank.id}>
                      {bank.icon} {bank.name} - {formatCurrency(bank.currentBalance)}
                    </option>
                  ))}
                </select>
              </div>

              {selectedBankId && (
                <div className="bg-blue-50 p-3 rounded-lg">
                  <p className="text-sm text-blue-700">
                    <strong>Saldo disponível:</strong> {formatCurrency(banks.find(b => b.id === selectedBankId)?.currentBalance || 0)}
                  </p>
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowDepositModal(false)
                  setSelectedSavings(null)
                  setSelectedBankId('')
                  setTransactionAmount(0)
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleDeposit}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Depositar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Sacar */}
      {showWithdrawModal && selectedSavings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Sacar do Cofrinho</h3>
            <p className="text-gray-600 mb-4">{selectedSavings.icon} {selectedSavings.name}</p>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Valor</label>
                <CurrencyInput
                  value={transactionAmount}
                  onChange={setTransactionAmount}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="R$ 0,00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Banco de Destino</label>
                <select
                  value={selectedBankId}
                  onChange={(e) => setSelectedBankId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Selecione um banco</option>
                  {banks.map((bank) => (
                    <option key={bank.id} value={bank.id}>
                      {bank.icon} {bank.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="bg-red-50 p-3 rounded-lg">
                <p className="text-sm text-red-700">
                  <strong>Saldo disponível no cofrinho:</strong> {formatCurrency(selectedSavings.currentAmount)}
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowWithdrawModal(false)
                  setSelectedSavings(null)
                  setSelectedBankId('')
                  setTransactionAmount(0)
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleWithdraw}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Sacar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default SavingsManager
