const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Listar assinaturas do usuário
router.get('/', async (req, res) => {
  try {
    const subscriptions = await prisma.subscription.findMany({
      where: { userId: req.user.id },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      },
      orderBy: { name: 'asc' }
    });

    res.json(subscriptions);
  } catch (error) {
    console.error('Erro ao buscar assinaturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar assinatura
router.post('/', async (req, res) => {
  try {
    const { name, description, amount, startDate, paymentMethodId, categoryId } = req.body;

    if (!name || !amount || !startDate || !paymentMethodId) {
      return res.status(400).json({ error: 'Nome, valor, data de início e forma de pagamento são obrigatórios' });
    }

    // Verificar se a forma de pagamento é um cartão de crédito
    const paymentMethod = await prisma.paymentMethod.findFirst({
      where: { id: paymentMethodId, userId: req.user.id, type: 'CREDIT' }
    });

    if (!paymentMethod) {
      return res.status(400).json({ error: 'Apenas cartões de crédito são aceitos para assinaturas' });
    }

    // Calcular próxima data de cobrança baseada na fatura do cartão
    const nextBillDate = new Date(paymentMethod.billDueDate);

    const subscription = await prisma.subscription.create({
      data: {
        name,
        description: description || null,
        amount: parseFloat(amount),
        startDate: new Date(startDate),
        nextBillDate: nextBillDate,
        paymentMethodId,
        categoryId: categoryId || null,
        userId: req.user.id
      },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      }
    });

    res.status(201).json(subscription);
  } catch (error) {
    console.error('Erro ao criar assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar assinatura
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, amount, isActive, categoryId } = req.body;

    const subscription = await prisma.subscription.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!subscription) {
      return res.status(404).json({ error: 'Assinatura não encontrada' });
    }

    const updatedSubscription = await prisma.subscription.update({
      where: { id },
      data: {
        name: name || subscription.name,
        description: description !== undefined ? description : subscription.description,
        amount: amount !== undefined ? parseFloat(amount) : subscription.amount,
        isActive: isActive !== undefined ? isActive : subscription.isActive,
        categoryId: categoryId !== undefined ? categoryId : subscription.categoryId
      },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      }
    });

    res.json(updatedSubscription);
  } catch (error) {
    console.error('Erro ao atualizar assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar assinatura
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const subscription = await prisma.subscription.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!subscription) {
      return res.status(404).json({ error: 'Assinatura não encontrada' });
    }

    await prisma.subscription.delete({
      where: { id }
    });

    res.json({ message: 'Assinatura excluída com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Processar assinaturas vencidas (para ser chamado por um cron job)
router.post('/process-bills', async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Buscar assinaturas que devem ser cobradas hoje
    const subscriptions = await prisma.subscription.findMany({
      where: {
        isActive: true,
        nextBillDate: {
          lte: today
        }
      },
      include: {
        paymentMethod: true,
        category: true
      }
    });

    let processedCount = 0;

    for (const subscription of subscriptions) {
      try {
        // Adicionar valor à fatura do cartão
        await prisma.paymentMethod.update({
          where: { id: subscription.paymentMethodId },
          data: {
            currentBill: subscription.paymentMethod.currentBill + subscription.amount,
            isBillPaid: false
          }
        });

        // Criar transação automática
        await prisma.transaction.create({
          data: {
            description: `${subscription.name} (Assinatura)`,
            amount: subscription.amount,
            type: 'EXPENSE',
            categoryId: subscription.categoryId,
            paymentMethodId: subscription.paymentMethodId,
            date: today,
            userId: subscription.userId
          }
        });

        // Atualizar próxima data de cobrança (próximo mês)
        const nextBillDate = new Date(subscription.nextBillDate);
        nextBillDate.setMonth(nextBillDate.getMonth() + 1);

        await prisma.subscription.update({
          where: { id: subscription.id },
          data: { nextBillDate }
        });

        processedCount++;
      } catch (error) {
        console.error(`Erro ao processar assinatura ${subscription.id}:`, error);
      }
    }

    res.json({ 
      message: `${processedCount} assinaturas processadas com sucesso`,
      processedCount 
    });
  } catch (error) {
    console.error('Erro ao processar assinaturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
