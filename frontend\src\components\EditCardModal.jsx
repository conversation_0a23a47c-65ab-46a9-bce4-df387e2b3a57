import React, { useState, useEffect } from 'react'
import { X, Save, Palette, Calendar, Tag } from 'lucide-react'
import api from '../services/api'
import { toast } from 'react-hot-toast'

function EditCardModal({ isOpen, onClose, card, onUpdate }) {
  const [formData, setFormData] = useState({
    title: '',
    categories: [],
    dataSource: 'categories',
    selectedItems: [],
    config: {
      color: '#475569',
      period: 'year'
    }
  })
  const [categories, setCategories] = useState([])
  const [banks, setBanks] = useState([])
  const [savings, setSavings] = useState([])
  const [subscriptions, setSubscriptions] = useState([])
  const [paymentMethods, setPaymentMethods] = useState([])
  const [loading, setLoading] = useState(false)
  const [showCustomColor, setShowCustomColor] = useState(false)
  const [customColor, setCustomColor] = useState('#475569')

  const colors = [
    // Vermelhos
    '#EF4444', '#DC2626', '#B91C1C', '#991B1B',
    // Laranjas
    '#F97316', '#EA580C', '#C2410C', '#9A3412',
    // Amarelos
    '#EAB308', '#CA8A04', '#A16207', '#854D0E',
    // Verdes
    '#22C55E', '#16A34A', '#15803D', '#166534',
    // Azuis
    '#3B82F6', '#2563EB', '#1D4ED8', '#1E40AF',
    // Índigos
    '#6366F1', '#4F46E5', '#4338CA', '#3730A3',
    // Roxos
    '#8B5CF6', '#7C3AED', '#6D28D9', '#5B21B6',
    // Rosas
    '#EC4899', '#DB2777', '#BE185D', '#9D174D',
    // Cinzas
    '#6B7280', '#4B5563', '#374151', '#1F2937'
  ]

  useEffect(() => {
    if (isOpen && card) {
      const cardColor = card.config?.color || '#475569'
      const isCustomColor = !colors.includes(cardColor)

      setFormData({
        title: card.title || '',
        categories: card.categories || [],
        dataSource: card.dataSource || 'categories',
        selectedItems: card.selectedItems || [],
        config: {
          color: cardColor,
          period: card.config?.period || 'year'
        }
      })

      if (isCustomColor) {
        setShowCustomColor(true)
        setCustomColor(cardColor)
      } else {
        setShowCustomColor(false)
        setCustomColor('#475569')
      }

      fetchAllData()
    }
  }, [isOpen, card])

  const fetchAllData = async () => {
    try {
      // Buscar categorias
      const categoriesResponse = await api.get('/categories')
      setCategories(categoriesResponse.data)

      // Buscar bancos
      const banksResponse = await api.get('/banks')
      setBanks(banksResponse.data)

      // Buscar cofrinhos
      const savingsResponse = await api.get('/banks/savings')
      setSavings(savingsResponse.data)

      // Buscar assinaturas
      const subscriptionsResponse = await api.get('/subscriptions')
      setSubscriptions(subscriptionsResponse.data)

      // Buscar métodos de pagamento (para faturas)
      const paymentMethodsResponse = await api.get('/payment-methods')
      setPaymentMethods(paymentMethodsResponse.data.filter(pm => pm.type === 'CREDIT'))
    } catch (error) {
      console.error('Erro ao buscar dados:', error)
      toast.error('Erro ao carregar dados')
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!formData.title.trim()) {
      toast.error('Título é obrigatório')
      return
    }

    if (formData.dataSource === 'categories' && formData.categories.length === 0) {
      toast.error('Selecione pelo menos uma categoria')
      return
    }

    if (formData.dataSource !== 'categories' && formData.selectedItems.length === 0) {
      toast.error('Selecione pelo menos um item')
      return
    }

    setLoading(true)
    try {
      const response = await api.put(`/dashboard-cards/${card.id}`, formData)
      onUpdate(response.data)
      toast.success('Card atualizado com sucesso!')
      onClose()
    } catch (error) {
      console.error('Erro ao atualizar card:', error)
      toast.error('Erro ao atualizar card')
    } finally {
      setLoading(false)
    }
  }

  const handleCategoryToggle = (categoryId) => {
    setFormData(prev => ({
      ...prev,
      categories: prev.categories.includes(categoryId)
        ? prev.categories.filter(id => id !== categoryId)
        : [...prev.categories, categoryId]
    }))
  }

  const handleItemToggle = (itemId) => {
    setFormData(prev => ({
      ...prev,
      selectedItems: prev.selectedItems.includes(itemId)
        ? prev.selectedItems.filter(id => id !== itemId)
        : [...prev.selectedItems, itemId]
    }))
  }

  const handleDataSourceChange = (dataSource) => {
    setFormData(prev => ({
      ...prev,
      dataSource,
      categories: dataSource === 'categories' ? prev.categories : [],
      selectedItems: dataSource !== 'categories' ? prev.selectedItems : []
    }))
  }

  const toggleAllCategories = () => {
    const allSelected = formData.categories.length === categories.length
    setFormData(prev => ({
      ...prev,
      categories: allSelected ? [] : categories.map(cat => cat.id)
    }))
  }

  const handleColorSelect = (color) => {
    setFormData(prev => ({
      ...prev,
      config: { ...prev.config, color }
    }))
    setShowCustomColor(false)
  }

  const handleCustomColorToggle = () => {
    if (!showCustomColor) {
      setShowCustomColor(true)
      setFormData(prev => ({
        ...prev,
        config: { ...prev.config, color: customColor }
      }))
    } else {
      setShowCustomColor(false)
      setFormData(prev => ({
        ...prev,
        config: { ...prev.config, color: colors[0] }
      }))
    }
  }

  const handleCustomColorChange = (color) => {
    setCustomColor(color)
    setFormData(prev => ({
      ...prev,
      config: { ...prev.config, color }
    }))
  }

  const dataSources = [
    {
      value: 'categories',
      name: 'Categorias',
      description: 'Baseado em categorias de transações',
      icon: '📊'
    },
    {
      value: 'banks',
      name: 'Bancos',
      description: 'Informações dos bancos e saldos',
      icon: '🏦'
    },
    {
      value: 'savings',
      name: 'Cofrinhos',
      description: 'Metas e economias',
      icon: '🐷'
    },
    {
      value: 'subscriptions',
      name: 'Assinaturas',
      description: 'Assinaturas recorrentes',
      icon: '📅'
    },
    {
      value: 'bills',
      name: 'Faturas',
      description: 'Faturas de cartões de crédito',
      icon: '💳'
    }
  ]

  const getCurrentItems = () => {
    switch (formData.dataSource) {
      case 'banks':
        return banks
      case 'savings':
        return savings
      case 'subscriptions':
        return subscriptions
      case 'bills':
        return paymentMethods
      default:
        return []
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Configurar Card</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Título */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Tag className="h-4 w-4 inline mr-1" />
              Título do Card
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500"
              placeholder="Ex: Gastos com Alimentação"
            />
          </div>

          {/* Período */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Calendar className="h-4 w-4 inline mr-1" />
              Período de Análise
            </label>
            <select
              value={formData.config.period}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                config: { ...prev.config, period: e.target.value }
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500"
            >
              <option value="month">Mensal</option>
              <option value="year">Anual</option>
            </select>
          </div>

          {/* Cor - apenas para cards numéricos e tabela */}
          {(card?.type === 'numeric' || card?.type === 'table') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Palette className="h-4 w-4 inline mr-1" />
                Cor do Card
              </label>

              {/* Cores predefinidas */}
              <div className="grid grid-cols-8 gap-2 mb-3">
                {colors.map((color) => (
                  <button
                    key={color}
                    type="button"
                    onClick={() => handleColorSelect(color)}
                    className={`w-10 h-10 rounded-lg border-2 transition-all hover:scale-105 ${
                      formData.config.color === color && !showCustomColor
                        ? 'border-gray-800 scale-110 shadow-lg'
                        : 'border-gray-300 hover:border-gray-500'
                    }`}
                    style={{ backgroundColor: color }}
                    title={color}
                  />
                ))}
              </div>

              {/* Opção de cor customizada */}
              <div className="border-t border-gray-200 pt-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Cor Customizada</span>
                  <button
                    type="button"
                    onClick={handleCustomColorToggle}
                    className={`px-3 py-1 text-xs rounded-md transition-colors ${
                      showCustomColor
                        ? 'bg-blue-100 text-blue-700 border border-blue-300'
                        : 'bg-gray-100 text-gray-600 border border-gray-300 hover:bg-gray-200'
                    }`}
                  >
                    {showCustomColor ? 'Usar Predefinida' : 'Personalizar'}
                  </button>
                </div>

                {showCustomColor && (
                  <div className="flex items-center space-x-3">
                    <input
                      type="color"
                      value={customColor}
                      onChange={(e) => handleCustomColorChange(e.target.value)}
                      className="w-12 h-10 rounded-lg border border-gray-300 cursor-pointer"
                    />
                    <input
                      type="text"
                      value={customColor}
                      onChange={(e) => handleCustomColorChange(e.target.value)}
                      placeholder="#000000"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm font-mono"
                    />
                    <div
                      className="w-10 h-10 rounded-lg border border-gray-300"
                      style={{ backgroundColor: customColor }}
                      title="Preview da cor"
                    />
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Aviso para cards de linha e pizza */}
          {(card?.type === 'chart' || card?.type === 'pie') && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-700">
                <span className="font-medium">💡 Dica:</span> As cores são atribuídas automaticamente para cada categoria,
                garantindo melhor diferenciação visual nos gráficos.
              </p>
            </div>
          )}

          {/* Fonte de Dados */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fonte de Dados
            </label>
            <div className="grid grid-cols-1 gap-2">
              {dataSources.map((source) => (
                <label key={source.value} className="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                  <input
                    type="radio"
                    name="dataSource"
                    value={source.value}
                    checked={formData.dataSource === source.value}
                    onChange={(e) => handleDataSourceChange(e.target.value)}
                    className="mr-3 text-blue-600 focus:ring-blue-500"
                  />
                  <div className="flex items-center flex-1">
                    <span className="text-2xl mr-3">{source.icon}</span>
                    <div>
                      <div className="font-medium text-gray-900">{source.name}</div>
                      <div className="text-sm text-gray-500">{source.description}</div>
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Categorias */}
          {formData.dataSource === 'categories' && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  Categorias
                </label>
                <button
                  type="button"
                  onClick={toggleAllCategories}
                  className="text-xs text-slate-600 hover:text-slate-700 font-medium"
                >
                  {formData.categories.length === categories.length ? 'Desmarcar Todas' : 'Selecionar Todas'}
                </button>
              </div>
              <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-lg p-3">
                <div className="space-y-2">
                  {categories.map((category) => (
                    <label key={category.id} className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.categories.includes(category.id)}
                        onChange={() => handleCategoryToggle(category.id)}
                        className="rounded border-gray-300 text-slate-600 focus:ring-slate-500"
                      />
                      <span className="text-lg">{category.icon}</span>
                      <span className="text-sm text-gray-700">{category.name}</span>
                    </label>
                  ))}
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {formData.categories.length} categoria(s) selecionada(s)
              </p>
            </div>
          )}

          {/* Itens da Fonte de Dados */}
          {formData.dataSource !== 'categories' && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  {dataSources.find(ds => ds.value === formData.dataSource)?.name || 'Itens'}
                </label>
              </div>
              <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-lg p-3">
                <div className="space-y-2">
                  {getCurrentItems().map((item) => (
                    <label key={item.id} className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.selectedItems.includes(item.id)}
                        onChange={() => handleItemToggle(item.id)}
                        className="rounded border-gray-300 text-slate-600 focus:ring-slate-500"
                      />
                      <span className="text-lg">{item.icon || '📄'}</span>
                      <span className="text-sm text-gray-700">{item.name}</span>
                    </label>
                  ))}
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {formData.selectedItems.length} item(s) selecionado(s)
              </p>
            </div>
          )}

          {/* Botões */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-gradient-to-r from-slate-700 to-slate-800 text-white rounded-lg hover:from-slate-800 hover:to-slate-900 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              <Save className="h-4 w-4" />
              <span>{loading ? 'Salvando...' : 'Salvar'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default EditCardModal
