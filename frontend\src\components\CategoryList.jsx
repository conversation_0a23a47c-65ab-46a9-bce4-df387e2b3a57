import React, { useState } from 'react'
import { Edit2, Trash2, ChevronRight, ChevronDown, Plus } from 'lucide-react'
import { categoryService } from '../services/categoryService'

function CategoryList({ categories, onEdit, onDelete, onCreateSubcategory }) {
  const [expandedCategories, setExpandedCategories] = useState(new Set())
  const organizedCategories = categoryService.organizeCategories(categories)

  const toggleCategory = (categoryId) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev)
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId)
      } else {
        newSet.add(categoryId)
      }
      return newSet
    })
  }

  const renderCategory = (category, isSubcategory = false) => {
    const hasSubcategories = !isSubcategory && category.subcategories?.length > 0
    const isExpanded = expandedCategories.has(category.id)

    return (
      <div 
        key={category.id} 
        className={`bg-white rounded-xl border transition-all duration-200 ${
          isSubcategory 
            ? 'border-gray-100 hover:border-blue-200 ml-8' 
            : 'border-gray-200 hover:border-blue-300'
        }`}
      >
        <div className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {hasSubcategories && (
                <button
                  onClick={() => toggleCategory(category.id)}
                  className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              )}
              <div 
                className={`w-10 h-10 rounded-lg flex items-center justify-center text-2xl`}
                style={{ backgroundColor: category.color }}
              >
                {category.icon}
              </div>
              <div>
                <h3 className="font-medium text-gray-900">{category.name}</h3>
                {isSubcategory && (
                  <p className="text-sm text-gray-500">Sub-categoria</p>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {!isSubcategory && (
                <button
                  onClick={() => onCreateSubcategory(category)}
                  className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                  title="Adicionar sub-categoria"
                >
                  <Plus className="h-4 w-4" />
                </button>
              )}
              <button
                onClick={() => onEdit(category)}
                className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                title="Editar categoria"
              >
                <Edit2 className="h-4 w-4" />
              </button>
              <button
                onClick={() => onDelete(category)}
                className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                title="Deletar categoria"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Sub-categorias */}
        {hasSubcategories && isExpanded && (
          <div className="border-t border-gray-100 mt-2 p-4 space-y-3 bg-gray-50 rounded-b-xl">
            {category.subcategories.map(sub => renderCategory(sub, true))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {organizedCategories.map(category => renderCategory(category))}
    </div>
  )
}

export default CategoryList 