const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Iniciando seed do banco de dados...');

  // Criar usuário de exemplo
  const hashedPassword = await bcrypt.hash('123456', 12);
  
  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Administrador',
      password: hashedPassword,
    },
  });

  console.log('👤 Usuário criado:', user.email);

  // Criar categorias
  const categories = await Promise.all([
    prisma.category.upsert({
      where: { id: 'cat-alimentacao' },
      update: {},
      create: {
        id: 'cat-alimentacao',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        color: '#EF4444',
        icon: '🍽️',
        userId: user.id,
      },
    }),
    prisma.category.upsert({
      where: { id: 'cat-transporte' },
      update: {},
      create: {
        id: 'cat-transporte',
        name: '<PERSON><PERSON>',
        color: '#3B82F6',
        icon: '🚗',
        userId: user.id,
      },
    }),
    prisma.category.upsert({
      where: { id: 'cat-moradia' },
      update: {},
      create: {
        id: 'cat-moradia',
        name: 'Moradia',
        color: '#10B981',
        icon: '🏠',
        userId: user.id,
      },
    }),
    prisma.category.upsert({
      where: { id: 'cat-saude' },
      update: {},
      create: {
        id: 'cat-saude',
        name: 'Saúde',
        color: '#F59E0B',
        icon: '🏥',
        userId: user.id,
      },
    }),
    prisma.category.upsert({
      where: { id: 'cat-salario' },
      update: {},
      create: {
        id: 'cat-salario',
        name: 'Salário',
        color: '#10B981',
        icon: '💰',
        userId: user.id,
      },
    }),
    prisma.category.upsert({
      where: { id: 'cat-investimentos' },
      update: {},
      create: {
        id: 'cat-investimentos',
        name: 'Investimentos',
        color: '#3B82F6',
        icon: '📈',
        userId: user.id,
      },
    }),
  ]);

  console.log('📂 Categorias criadas:', categories.length);

  // Criar transações de exemplo para o ano atual
  const currentYear = new Date().getFullYear();
  const transactions = [];

  // Gerar transações para cada mês do ano
  for (let month = 0; month < 12; month++) {
    // Salário mensal
    transactions.push({
      description: `Salário ${month + 1}/${currentYear}`,
      amount: 5000 + Math.random() * 1000, // Entre 5000 e 6000
      type: 'INCOME',
      date: new Date(currentYear, month, 5),
      categoryId: 'cat-salario',
      userId: user.id,
    });

    // Gastos com alimentação (3-5 por mês)
    const foodTransactions = Math.floor(Math.random() * 3) + 3;
    for (let i = 0; i < foodTransactions; i++) {
      transactions.push({
        description: `Supermercado ${i + 1}`,
        amount: 150 + Math.random() * 200, // Entre 150 e 350
        type: 'EXPENSE',
        date: new Date(currentYear, month, Math.floor(Math.random() * 28) + 1),
        categoryId: 'cat-alimentacao',
        userId: user.id,
      });
    }

    // Gastos com transporte
    transactions.push({
      description: 'Combustível',
      amount: 200 + Math.random() * 100, // Entre 200 e 300
      type: 'EXPENSE',
      date: new Date(currentYear, month, Math.floor(Math.random() * 28) + 1),
      categoryId: 'cat-transporte',
      userId: user.id,
    });

    // Gastos com moradia
    transactions.push({
      description: 'Aluguel',
      amount: 1200 + Math.random() * 300, // Entre 1200 e 1500
      type: 'EXPENSE',
      date: new Date(currentYear, month, 10),
      categoryId: 'cat-moradia',
      userId: user.id,
    });

    // Gastos com saúde (ocasionais)
    if (Math.random() > 0.7) {
      transactions.push({
        description: 'Consulta médica',
        amount: 100 + Math.random() * 200, // Entre 100 e 300
        type: 'EXPENSE',
        date: new Date(currentYear, month, Math.floor(Math.random() * 28) + 1),
        categoryId: 'cat-saude',
        userId: user.id,
      });
    }

    // Investimentos (ocasionais)
    if (Math.random() > 0.6) {
      transactions.push({
        description: 'Investimento em ações',
        amount: 500 + Math.random() * 1000, // Entre 500 e 1500
        type: 'INVESTMENT',
        date: new Date(currentYear, month, Math.floor(Math.random() * 28) + 1),
        categoryId: 'cat-investimentos',
        userId: user.id,
      });
    }
  }

  // Inserir todas as transações
  for (const transaction of transactions) {
    await prisma.transaction.create({
      data: transaction,
    });
  }

  console.log('💰 Transações criadas:', transactions.length);
  console.log('✅ Seed concluído com sucesso!');
  console.log('');
  console.log('📧 Email: <EMAIL>');
  console.log('🔑 Senha: 123456');
}

main()
  .catch((e) => {
    console.error('❌ Erro no seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
