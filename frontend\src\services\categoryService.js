import api from './api'

export const categoryService = {
  async getCategories() {
    const response = await api.get('/categories')
    return response.data
  },

  async createCategory(categoryData) {
    const response = await api.post('/categories', categoryData)
    return response.data
  },

  async updateCategory(id, categoryData) {
    const response = await api.put(`/categories/${id}`, categoryData)
    return response.data
  },

  async deleteCategory(id) {
    const response = await api.delete(`/categories/${id}`)
    return response.data
  },

  // Função auxiliar para organizar categorias em hierarquia
  organizeCategories(categories) {
    const mainCategories = categories.filter(c => !c.isSubcategory)
    const subCategories = categories.filter(c => c.isSubcategory)

    return mainCategories.map(category => ({
      ...category,
      subcategories: subCategories.filter(sub => sub.parentId === category.id)
    }))
  }
} 