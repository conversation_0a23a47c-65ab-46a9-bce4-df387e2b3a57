# 🌙 Sara - Sistema de Gastos Monetários

Um sistema completo para controle de gastos pessoais com dashboard interativo, desenvolvido com React e Node.js.

## 🚀 Funcionalidades

- ✅ **Sistema de Login/Registro** com autenticação JWT
- ✅ **Senhas criptografadas** com bcrypt
- ✅ **Dashboard interativo** com métricas e gráficos
- ✅ **Gestão de transações** (receitas, despesas, investimentos, empréstimos)
- ✅ **Categorias personalizáveis** com ícones e cores
- ✅ **Gráficos dinâmicos** de saldo mensal e receitas/despesas
- ✅ **Filtros avançados** para transações
- ✅ **Design responsivo** similar ao layout mostrado
- ✅ **API REST** completa com validações

## 🛠️ Tecnologias

### Backend
- **Node.js** + Express
- **Prisma ORM** + SQLite
- **JWT** para autenticação
- **bcrypt** para criptografia de senhas
- **Helmet** + Rate limiting para segurança

### Frontend
- **React** + Vite
- **Tailwind CSS** para estilização
- **Recharts** para gráficos
- **React Hook Form** para formulários
- **Axios** para requisições HTTP
- **React Router** para navegação

## 📦 Instalação

### Pré-requisitos
- Node.js 16+ 
- npm ou yarn

### 1. Clone o repositório
```bash
git clone <url-do-repositorio>
cd sara
```

### 2. Instale as dependências
```bash
# Instalar dependências do projeto principal
npm install

# Instalar dependências do backend e frontend
npm run install:all
```

### 3. Configure o banco de dados
```bash
# Navegar para o backend
cd backend

# Gerar o cliente Prisma
npm run db:generate

# Criar o banco de dados
npm run db:push

# Popular com dados de exemplo (opcional)
npm run db:seed
```

### 4. Configure as variáveis de ambiente
Edite o arquivo `backend/.env` se necessário:
```env
DATABASE_URL="file:./dev.db"
JWT_SECRET="sua_chave_secreta_muito_segura_aqui_123456789"
PORT=3001
NODE_ENV=development
```

## 🚀 Executando o projeto

### Desenvolvimento (Backend + Frontend)
```bash
# Na raiz do projeto
npm run dev
```

Isso iniciará:
- Backend na porta 3001: http://localhost:3001
- Frontend na porta 5173: http://localhost:5173

### Executar separadamente

#### Backend apenas
```bash
cd backend
npm run dev
```

#### Frontend apenas
```bash
cd frontend
npm run dev
```

## 👤 Usuário de exemplo

Se você executou o seed, pode usar:
- **Email:** <EMAIL>
- **Senha:** 123456

## 📱 Como usar

1. **Acesse** http://localhost:5173
2. **Faça login** ou crie uma nova conta
3. **Explore o dashboard** com métricas e gráficos
4. **Adicione transações** usando o botão "Nova Transação"
5. **Gerencie categorias** na página de categorias
6. **Use filtros** para encontrar transações específicas

## 🎨 Layout

O sistema replica o design mostrado na imagem com:
- **Sidebar escura** com menu de navegação
- **Header** com seletor de ano
- **Cards de métricas** com valores formatados em reais
- **Gráfico circular** para lucro líquido
- **Gráficos de linha e barras** para análise temporal
- **Design responsivo** para mobile e desktop

## 📊 Estrutura do projeto

```
sara/
├── backend/                 # API Node.js
│   ├── src/
│   │   ├── routes/         # Rotas da API
│   │   ├── middleware/     # Middlewares
│   │   └── server.js       # Servidor principal
│   ├── prisma/
│   │   └── schema.prisma   # Schema do banco
│   └── package.json
├── frontend/               # App React
│   ├── src/
│   │   ├── components/     # Componentes reutilizáveis
│   │   ├── pages/          # Páginas da aplicação
│   │   ├── contexts/       # Context API
│   │   └── services/       # Serviços (API)
│   └── package.json
└── package.json           # Scripts principais
```

## 🔒 Segurança

- Senhas criptografadas com bcrypt (salt rounds: 12)
- Autenticação JWT com expiração de 7 dias
- Rate limiting (100 requests por 15 minutos)
- Helmet para headers de segurança
- Validação de dados no backend
- CORS configurado

## 🚀 Deploy

### Backend
1. Configure as variáveis de ambiente de produção
2. Execute `npm run build` no frontend
3. Configure o servidor para servir os arquivos estáticos
4. Execute `npm start` no backend

### Frontend
O frontend é uma SPA que pode ser hospedada em qualquer serviço de hosting estático (Vercel, Netlify, etc.)

## 🤝 Contribuindo

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT.

---

Desenvolvido com ❤️ para controle financeiro pessoal
