@echo off
echo ========================================
echo    🌙 Sara - Sistema de Gastos
echo    Instalacao Automatica
echo ========================================
echo.

echo ⏳ Verificando Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js nao encontrado!
    echo.
    echo 📥 Por favor, instale o Node.js:
    echo 1. Acesse: https://nodejs.org/
    echo 2. Baixe a versao LTS
    echo 3. Execute o instalador
    echo 4. Reinicie este script
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js encontrado!
node --version

echo.
echo ⏳ Instalando dependencias do backend...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Erro ao instalar dependencias do backend
    pause
    exit /b 1
)

echo.
echo ⏳ Configurando banco de dados...
if exist dev.db del dev.db
if exist prisma\dev.db del prisma\dev.db
call npx prisma generate
call npx prisma db push
if %errorlevel% neq 0 (
    echo ❌ Erro ao configurar banco de dados
    pause
    exit /b 1
)

echo.
echo ⏳ Populando banco com dados de exemplo...
call node src/seed.js
if %errorlevel% neq 0 (
    echo ⚠️  Aviso: Erro ao popular banco (pode ser ignorado)
)

echo.
echo ⏳ Instalando dependencias do frontend...
cd ../frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Erro ao instalar dependencias do frontend
    pause
    exit /b 1
)

echo.
echo ⏳ Instalando dependencias principais...
cd ..
call npm install
if %errorlevel% neq 0 (
    echo ❌ Erro ao instalar dependencias principais
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ Instalacao concluida com sucesso!
echo ========================================
echo.
echo 🚀 Para iniciar o sistema, execute:
echo    npm run dev
echo.
echo 🌐 Depois acesse: http://localhost:5173
echo.
echo 👤 Credenciais de teste:
echo    Email: <EMAIL>
echo    Senha: 123456
echo.
echo ========================================
pause
