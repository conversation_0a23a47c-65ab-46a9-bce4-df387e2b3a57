@echo off
echo ========================================
echo    🔄 Sara - Atualizando Banco de Dados
echo    Adicionando novas funcionalidades
echo ========================================
echo.

echo ⏳ Navegando para o backend...
cd backend

echo ⏳ Gerando cliente Prisma atualizado...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ Erro ao gerar cliente Prisma
    pause
    exit /b 1
)

echo ⏳ Aplicando mudanças no banco de dados...
call npx prisma db push
if %errorlevel% neq 0 (
    echo ❌ Erro ao aplicar mudanças no banco
    pause
    exit /b 1
)

echo ⏳ Criando perfis padrão para usuários existentes...
call node src/scripts/createDefaultProfile.js
if %errorlevel% neq 0 (
    echo ❌ Erro ao criar perfis padrão
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ Banco de dados atualizado com sucesso!
echo ========================================
echo.
echo 🆕 Sistema Financeiro Completo - Transações Avançadas:
echo.
echo 💳 SISTEMA DE FATURAS DE CARTÃO:
echo   ✅ Campos de fatura em cartões de crédito
echo   ✅ Dia de vencimento obrigatório
echo   ✅ Cálculo automático de próxima fatura
echo   ✅ Status de pagamento (pago/pendente)
echo   ✅ API para pagamento de faturas
echo   ✅ Atualização automática com transações
echo   ✅ Interface de gestão de faturas
echo.
echo 📸 INTEGRAÇÃO CLOUDINARY:
echo   ✅ Upload de comprovantes (fotos/PDFs)
echo   ✅ Middleware multer configurado
echo   ✅ Validação de tipos de arquivo
echo   ✅ Armazenamento organizado em pastas
echo   ✅ URLs seguras para visualização
echo   ✅ Interface de upload drag-and-drop
echo.
echo 📊 TRANSAÇÕES AVANÇADAS:
echo   ✅ Filtros avançados por data, valor, categoria
echo   ✅ Busca por descrição
echo   ✅ Filtro por banco e forma de pagamento
echo   ✅ Visualização de comprovantes
echo   ✅ Design profissional da tabela
echo   ✅ Upload de comprovantes na criação
echo   ✅ Ícones visuais por tipo de transação
echo.
echo 🔄 SISTEMA DE ASSINATURAS:
echo   ✅ Modelo Subscription completo
echo   ✅ Vinculação a cartões de crédito
echo   ✅ Cobrança automática mensal
echo   ✅ Status ativo/pausado
echo   ✅ Processamento automático de faturas
echo   ✅ Interface de gestão completa
echo   ✅ Resumo financeiro de assinaturas
echo.
echo 🧾 GESTÃO DE FATURAS:
echo   ✅ Dashboard de faturas pendentes
echo   ✅ Status visual (paga/pendente/vencida)
echo   ✅ Alertas de vencimento
echo   ✅ Pagamento com um clique
echo   ✅ Resumo financeiro completo
echo   ✅ Integração com saldos bancários
echo.
echo 🚀 Agora você pode iniciar o sistema:
echo    cd ..
echo    npm run dev
echo.
echo 🌐 Acesse: http://localhost:5173
echo 👤 Login: <EMAIL> / Senha: 123456
echo.
pause
