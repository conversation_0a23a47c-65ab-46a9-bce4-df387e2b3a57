import React, { useState, useEffect } from 'react'
import { Plus, Edit3, Trash2, Play, Pause, Calendar, CreditCard, DollarSign } from 'lucide-react'
import { subscriptionService, paymentMethodService } from '../services/bankService'
import CurrencyInput from './CurrencyInput'
import toast from 'react-hot-toast'

function SubscriptionManager() {
  const [subscriptions, setSubscriptions] = useState([])
  const [paymentMethods, setPaymentMethods] = useState([])
  const [categories, setCategories] = useState([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [editingSubscription, setEditingSubscription] = useState(null)
  const [subscriptionAmount, setSubscriptionAmount] = useState(0)

  const [newSubscription, setNewSubscription] = useState({
    name: '',
    description: '',
    amount: 0,
    startDate: new Date().toISOString().split('T')[0],
    paymentMethodId: '',
    categoryId: ''
  })

  useEffect(() => {
    fetchSubscriptions()
    fetchPaymentMethods()
    fetchCategories()
  }, [])

  const fetchSubscriptions = async () => {
    try {
      const data = await subscriptionService.getSubscriptions()
      setSubscriptions(data)
    } catch (error) {
      toast.error('Erro ao carregar assinaturas')
    } finally {
      setLoading(false)
    }
  }

  const fetchPaymentMethods = async () => {
    try {
      const data = await paymentMethodService.getPaymentMethods()
      // Filtrar apenas cartões de crédito
      const creditCards = data.filter(method => method.type === 'CREDIT')
      setPaymentMethods(creditCards)
    } catch (error) {
      console.error('Erro ao carregar formas de pagamento:', error)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      const data = await response.json()
      setCategories(data)
    } catch (error) {
      console.error('Erro ao carregar categorias:', error)
    }
  }

  const handleCreateSubscription = async () => {
    try {
      if (!newSubscription.name || !newSubscription.paymentMethodId || subscriptionAmount <= 0) {
        toast.error('Nome, cartão de crédito e valor são obrigatórios')
        return
      }

      const subscriptionData = {
        ...newSubscription,
        amount: subscriptionAmount
      }

      await subscriptionService.createSubscription(subscriptionData)
      toast.success('Assinatura criada com sucesso!')
      setShowAddModal(false)
      setNewSubscription({
        name: '', description: '', amount: 0,
        startDate: new Date().toISOString().split('T')[0],
        paymentMethodId: '', categoryId: ''
      })
      setSubscriptionAmount(0)
      fetchSubscriptions()
    } catch (error) {
      toast.error(error.response?.data?.error || 'Erro ao criar assinatura')
    }
  }

  const handleUpdateSubscription = async () => {
    try {
      const subscriptionData = {
        ...editingSubscription,
        amount: subscriptionAmount
      }

      await subscriptionService.updateSubscription(editingSubscription.id, subscriptionData)
      toast.success('Assinatura atualizada com sucesso!')
      setShowEditModal(false)
      setEditingSubscription(null)
      setSubscriptionAmount(0)
      fetchSubscriptions()
    } catch (error) {
      toast.error('Erro ao atualizar assinatura')
    }
  }

  const handleDeleteSubscription = async (subscription) => {
    if (!confirm(`Tem certeza que deseja excluir a assinatura "${subscription.name}"?`)) {
      return
    }

    try {
      await subscriptionService.deleteSubscription(subscription.id)
      toast.success('Assinatura excluída com sucesso!')
      fetchSubscriptions()
    } catch (error) {
      toast.error('Erro ao excluir assinatura')
    }
  }

  const toggleSubscriptionStatus = async (subscription) => {
    try {
      await subscriptionService.updateSubscription(subscription.id, {
        isActive: !subscription.isActive
      })
      toast.success(`Assinatura ${!subscription.isActive ? 'ativada' : 'pausada'} com sucesso!`)
      fetchSubscriptions()
    } catch (error) {
      toast.error('Erro ao alterar status da assinatura')
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('pt-BR')
  }

  const getDaysUntilNextBill = (nextBillDate) => {
    const today = new Date()
    const billDate = new Date(nextBillDate)
    const diffTime = billDate - today
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Assinaturas</h1>
          <p className="text-gray-600 mt-1">Gerencie suas assinaturas recorrentes</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-slate-900 rounded-lg hover:bg-slate-800 transition-colors"
        >
          <Plus className="h-4 w-4" />
          <span>Nova Assinatura</span>
        </button>
      </div>

      {/* Resumo */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-2xl border shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Mensal</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(subscriptions.filter(s => s.isActive).reduce((sum, s) => sum + s.amount, 0))}
              </p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl border shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Assinaturas Ativas</p>
              <p className="text-2xl font-bold text-gray-900">
                {subscriptions.filter(s => s.isActive).length}
              </p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <Play className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl border shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Próxima Cobrança</p>
              <p className="text-2xl font-bold text-gray-900">
                {subscriptions.filter(s => s.isActive).length > 0 ? (
                  Math.min(...subscriptions.filter(s => s.isActive).map(s => getDaysUntilNextBill(s.nextBillDate))) + ' dias'
                ) : '-'}
              </p>
            </div>
            <div className="bg-orange-100 p-3 rounded-full">
              <Calendar className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Lista de Assinaturas */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {subscriptions.map((subscription) => (
          <div
            key={subscription.id}
            className={`bg-white rounded-2xl border shadow-sm p-6 transition-all hover:shadow-md ${
              !subscription.isActive ? 'opacity-60 bg-gray-50' : ''
            }`}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center text-white text-xl font-bold">
                  {subscription.name.charAt(0).toUpperCase()}
                </div>
                <div>
                  <h3 className="font-bold text-gray-900">{subscription.name}</h3>
                  <div className="flex items-center gap-2 mt-1">
                    <div className={`w-2 h-2 rounded-full ${subscription.isActive ? 'bg-green-500' : 'bg-gray-400'}`} />
                    <p className="text-sm text-gray-500">
                      {subscription.isActive ? 'Ativa' : 'Pausada'}
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <button
                  onClick={() => toggleSubscriptionStatus(subscription)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  title={subscription.isActive ? 'Pausar' : 'Ativar'}
                >
                  {subscription.isActive ?
                    <Pause className="h-4 w-4 text-orange-600" /> :
                    <Play className="h-4 w-4 text-green-600" />
                  }
                </button>
                <button
                  onClick={() => {
                    setEditingSubscription(subscription)
                    setSubscriptionAmount(subscription.amount)
                    setShowEditModal(true)
                  }}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Editar"
                >
                  <Edit3 className="h-4 w-4 text-gray-600" />
                </button>
                <button
                  onClick={() => handleDeleteSubscription(subscription)}
                  className="p-2 hover:bg-red-50 rounded-lg text-red-600 transition-colors"
                  title="Excluir"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="space-y-3">
              <div className="bg-gray-50 rounded-xl p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-600">Valor Mensal</span>
                  <span className="text-xl font-bold text-gray-900">
                    {formatCurrency(subscription.amount)}
                  </span>
                </div>
              </div>

              {subscription.paymentMethod && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <CreditCard className="h-4 w-4" />
                  <span>{subscription.paymentMethod.icon} {subscription.paymentMethod.name}</span>
                  {subscription.paymentMethod.bank && (
                    <span className="text-gray-400">({subscription.paymentMethod.bank.name})</span>
                  )}
                </div>
              )}

              {subscription.category && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <span>{subscription.category.icon}</span>
                  <span>{subscription.category.name}</span>
                </div>
              )}

              {subscription.isActive && (
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">
                    Próxima cobrança: {formatDate(subscription.nextBillDate)}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    getDaysUntilNextBill(subscription.nextBillDate) <= 7
                      ? 'bg-red-100 text-red-700'
                      : 'bg-blue-100 text-blue-700'
                  }`}>
                    {getDaysUntilNextBill(subscription.nextBillDate)} dias
                  </span>
                </div>
              )}

              {subscription.description && (
                <p className="text-sm text-gray-500 mt-2">{subscription.description}</p>
              )}
            </div>
          </div>
        ))}
      </div>

      {subscriptions.length === 0 && (
        <div className="text-center py-12">
          <CreditCard className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhuma assinatura cadastrada</h3>
          <p className="text-gray-600 mb-4">Comece criando sua primeira assinatura recorrente</p>
          <button
            onClick={() => setShowAddModal(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Criar Primeira Assinatura</span>
          </button>
        </div>
      )}

      {/* Modal Adicionar Assinatura */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Nova Assinatura</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                <input
                  type="text"
                  value={newSubscription.name}
                  onChange={(e) => setNewSubscription({ ...newSubscription, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: Netflix, Spotify..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
                <textarea
                  value={newSubscription.description}
                  onChange={(e) => setNewSubscription({ ...newSubscription, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Descrição opcional..."
                  rows="2"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Valor Mensal</label>
                <CurrencyInput
                  value={subscriptionAmount}
                  onChange={setSubscriptionAmount}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="R$ 0,00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Cartão de Crédito</label>
                <select
                  value={newSubscription.paymentMethodId}
                  onChange={(e) => setNewSubscription({ ...newSubscription, paymentMethodId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Selecione um cartão</option>
                  {paymentMethods.map((method) => (
                    <option key={method.id} value={method.id}>
                      {method.icon} {method.name} ({method.bank?.name})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Categoria</label>
                <select
                  value={newSubscription.categoryId}
                  onChange={(e) => setNewSubscription({ ...newSubscription, categoryId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Selecione uma categoria</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Data de Início</label>
                <input
                  type="date"
                  value={newSubscription.startDate}
                  onChange={(e) => setNewSubscription({ ...newSubscription, startDate: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowAddModal(false)
                  setNewSubscription({
                    name: '', description: '', amount: 0,
                    startDate: new Date().toISOString().split('T')[0],
                    paymentMethodId: '', categoryId: ''
                  })
                  setSubscriptionAmount(0)
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleCreateSubscription}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Criar Assinatura
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Editar Assinatura */}
      {showEditModal && editingSubscription && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Editar Assinatura</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                <input
                  type="text"
                  value={editingSubscription.name}
                  onChange={(e) => setEditingSubscription({ ...editingSubscription, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
                <textarea
                  value={editingSubscription.description || ''}
                  onChange={(e) => setEditingSubscription({ ...editingSubscription, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows="2"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Valor Mensal</label>
                <CurrencyInput
                  value={subscriptionAmount}
                  onChange={setSubscriptionAmount}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Categoria</label>
                <select
                  value={editingSubscription.categoryId || ''}
                  onChange={(e) => setEditingSubscription({ ...editingSubscription, categoryId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Selecione uma categoria</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowEditModal(false)
                  setEditingSubscription(null)
                  setSubscriptionAmount(0)
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleUpdateSubscription}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Salvar Alterações
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default SubscriptionManager
