import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'

// Página de teste simples
function TestPage() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            🎉 Frontend Funcionando!
          </h1>
          
          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h2 className="text-lg font-semibold text-green-800 mb-2">
                ✅ Sistema Carregado
              </h2>
              <p className="text-green-700 text-sm">
                React, Router e Tailwind estão funcionando corretamente.
              </p>
            </div>

            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h2 className="text-lg font-semibold text-blue-800 mb-2">
                🔧 Diagnóstico
              </h2>
              <div className="text-blue-700 text-sm space-y-1">
                <div>✅ React: Funcionando</div>
                <div>✅ Router: Funcionando</div>
                <div>✅ Tailwind: Funcionando</div>
                <div>✅ Toast: Funcionando</div>
              </div>
            </div>

            <button
              onClick={() => {
                console.log('Botão clicado!')
                alert('JavaScript funcionando!')
              }}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Testar JavaScript
            </button>

            <div className="text-xs text-gray-500 space-y-1">
              <div>URL atual: {window.location.href}</div>
              <div>Timestamp: {new Date().toLocaleString()}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Login simples
function SimpleLogin() {
  const [email, setEmail] = React.useState('')
  const [password, setPassword] = React.useState('')

  const handleSubmit = (e) => {
    e.preventDefault()
    // Simular login bem-sucedido
    localStorage.setItem('token', 'test-token')
    window.location.href = '/dashboard'
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-2xl font-bold text-center text-gray-900 mb-6">
          Login Simples
        </h1>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="<EMAIL>"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Senha
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="123456"
            />
          </div>
          
          <button
            type="submit"
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Entrar (Simulado)
          </button>
        </form>
        
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-xs text-yellow-700">
            Este é um login simulado para teste. Qualquer email/senha funcionará.
          </p>
        </div>
      </div>
    </div>
  )
}

// Dashboard simples
function SimpleDashboard() {
  const handleLogout = () => {
    localStorage.removeItem('token')
    window.location.href = '/login'
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-bold text-gray-900">
            Dashboard Simples
          </h1>
          <button
            onClick={handleLogout}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Sair
          </button>
        </div>
      </div>
      
      <div className="p-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-2">
              Sistema
            </h2>
            <p className="text-3xl font-bold text-green-600">OK</p>
            <p className="text-sm text-gray-600">Funcionando</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-2">
              Frontend
            </h2>
            <p className="text-3xl font-bold text-blue-600">✅</p>
            <p className="text-sm text-gray-600">Carregado</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-2">
              Teste
            </h2>
            <p className="text-3xl font-bold text-purple-600">100%</p>
            <p className="text-sm text-gray-600">Sucesso</p>
          </div>
        </div>
        
        <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Próximos Passos
          </h2>
          <ul className="space-y-2 text-sm text-gray-700">
            <li>✅ Frontend básico funcionando</li>
            <li>🔄 Testar autenticação real</li>
            <li>🔄 Conectar com backend</li>
            <li>🔄 Implementar funcionalidades completas</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

// Componente de rota protegida simples
function SimpleProtectedRoute({ children }) {
  const token = localStorage.getItem('token')
  return token ? children : <Navigate to="/login" />
}

function AppSimple() {
  return (
    <Router>
      <div className="App">
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
          }}
        />

        <Routes>
          <Route path="/login" element={<SimpleLogin />} />
          <Route path="/test" element={<TestPage />} />
          <Route 
            path="/dashboard" 
            element={
              <SimpleProtectedRoute>
                <SimpleDashboard />
              </SimpleProtectedRoute>
            } 
          />
          <Route path="/" element={<Navigate to="/test" />} />
        </Routes>
      </div>
    </Router>
  )
}

export default AppSimple
