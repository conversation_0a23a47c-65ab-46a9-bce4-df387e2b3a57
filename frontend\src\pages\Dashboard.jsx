import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  PiggyBank,
  CreditCard,
  Target,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Wallet,
  TrendingUpIcon,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Move,
  GripVertical
} from 'lucide-react'
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart
} from 'recharts'
import api from '../services/api'
import toast from 'react-hot-toast'
import SmartGreeting from '../components/SmartGreeting'
import { useAuth } from '../contexts/AuthContext'

function Dashboard({ selectedYear }) {
  const { user } = useAuth()
  const [dashboardData, setDashboardData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [chartOrder, setChartOrder] = useState(['balance', 'revenue'])
  const navigate = useNavigate()

  useEffect(() => {
    fetchDashboardData()
  }, [selectedYear])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/dashboard?year=${selectedYear || new Date().getFullYear()}`)
      setDashboardData(response.data)
    } catch (error) {
      console.error('Erro ao buscar dados do dashboard:', error)
      toast.error('Erro ao carregar dados do dashboard')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`
  }

  const getPercentageColor = (value) => {
    if (value > 0) return 'text-green-600'
    if (value < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  const getPercentageIcon = (value) => {
    if (value > 0) return <ArrowUpRight className="h-4 w-4" />
    if (value < 0) return <ArrowDownRight className="h-4 w-4" />
    return <TrendingUp className="h-4 w-4" />
  }

  const swapCharts = () => {
    setChartOrder(prev => [prev[1], prev[0]])
  }

  // Componente de Card Estatística
  const StatCard = ({ title, value, subtitle, change, icon: Icon, color = "blue" }) => {
    const colorClasses = {
      blue: "from-blue-500 to-blue-600",
      green: "from-green-500 to-green-600",
      red: "from-red-500 to-red-600",
      purple: "from-purple-500 to-purple-600",
      yellow: "from-yellow-500 to-yellow-600",
      indigo: "from-indigo-500 to-indigo-600"
    }

    return (
      <div className="relative overflow-hidden bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 scale-in">
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
              <p className="text-2xl font-bold text-gray-900 mb-1">{value}</p>
              {subtitle && (
                <p className="text-xs text-gray-500">{subtitle}</p>
              )}
            </div>
            <div className={`p-3 rounded-lg bg-gradient-to-r ${colorClasses[color]} text-white`}>
              <Icon className="h-6 w-6" />
            </div>
          </div>
          {change !== undefined && (
            <div className="mt-4 flex items-center">
              <div className={`flex items-center ${getPercentageColor(change)}`}>
                {getPercentageIcon(change)}
                <span className="ml-1 text-sm font-medium">
                  {Math.abs(change).toFixed(1)}%
                </span>
              </div>
              <span className="ml-2 text-xs text-gray-500">
                vs ano anterior
              </span>
            </div>
          )}
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!dashboardData) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Erro ao carregar dados do dashboard</p>
      </div>
    )
  }

  const { summary, charts } = dashboardData

  // Dados para o gráfico de linha (Saldo no final do mês)
  const monthlyBalanceData = charts.monthlyBalance.map((balance, index) => ({
    month: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
            'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'][index],
    balance
  }))

  // Dados para o gráfico de barras (Receitas e Despesas)
  const monthlyRevenueData = charts.monthlyData.map((data, index) => ({
    month: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
            'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'][index],
    income: Math.abs(data.income || 0), // Garantir valor positivo
    expenses: Math.abs(data.expenses || 0), // Garantir valor positivo
    net: (data.income || 0) - (data.expenses || 0) // Saldo líquido
  }))

  return (
    <div className="space-y-8">
      {/* Saudação Inteligente */}
      <SmartGreeting
        stats={dashboardData?.summary}
        recentTransactions={dashboardData?.recentTransactions || []}
      />

      {/* Resumo Financeiro Principal */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
        <div className="flex items-center mb-8">
          <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mr-5 shadow-lg">
            <DollarSign className="h-7 w-7 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Resumo Financeiro</h2>
            <p className="text-gray-600 mt-1">Visão geral das suas finanças em {selectedYear || new Date().getFullYear()}</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total de Receitas"
            value={formatCurrency(summary.totalIncome)}
            subtitle={`${formatCurrency(summary.avgMonthlyIncome)} por mês`}
            change={summary.incomeChange}
            icon={TrendingUp}
            color="green"
          />

          <StatCard
            title="Total de Gastos"
            value={formatCurrency(summary.totalExpenses)}
            subtitle={`${formatCurrency(summary.avgMonthlyExpenses)} por mês`}
            change={summary.expensesChange}
            icon={CreditCard}
            color="red"
          />

          <StatCard
            title="Saldo Líquido"
            value={formatCurrency(summary.liquidBalance)}
            subtitle={`${formatPercentage(summary.savingsRate)} de economia`}
            change={summary.incomeChange - summary.expensesChange}
            icon={Wallet}
            color="blue"
          />

          <StatCard
            title="Investimentos"
            value={formatCurrency(summary.totalInvestments)}
            subtitle={`${formatPercentage(summary.investmentRate)} da receita`}
            change={summary.investmentsChange}
            icon={TrendingUpIcon}
            color="purple"
          />
        </div>
      </div>

      {/* Análise Patrimonial */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
        <div className="flex items-center mb-8">
          <div className="w-14 h-14 bg-gradient-to-br from-purple-500 to-violet-600 rounded-2xl flex items-center justify-center mr-5 shadow-lg">
            <Target className="h-7 w-7 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Análise Patrimonial</h2>
            <p className="text-gray-600 mt-1">Patrimônio, taxas e extremos financeiros</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Patrimônio Líquido"
            value={formatCurrency(summary.netWorth)}
            subtitle="Receitas - Gastos + Investimentos"
            icon={Target}
            color="indigo"
          />

          <StatCard
            title="Taxa de Gastos"
            value={formatPercentage(summary.expenseRatio)}
            subtitle="Percentual da receita gasta"
            icon={BarChart3}
            color="yellow"
          />

          <StatCard
            title="Maior Receita Mensal"
            value={formatCurrency(summary.maxIncome)}
            subtitle={`Menor: ${formatCurrency(summary.minIncome || 0)}`}
            icon={ArrowUpRight}
            color="green"
          />

          <StatCard
            title="Maior Gasto Mensal"
            value={formatCurrency(summary.maxExpense)}
            subtitle={`Menor: ${formatCurrency(summary.minExpense || 0)}`}
            icon={ArrowDownRight}
            color="red"
          />
        </div>
      </div>

      {/* Gráfico de Lucro Líquido */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Lucro Líquido</h3>
          <div className="flex items-center justify-center">
            <div className="relative w-40 h-40">
              <svg className="w-40 h-40 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#E5E7EB"
                  strokeWidth="3"
                />
                <path
                  d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#3B82F6"
                  strokeWidth="3"
                  strokeDasharray={`${Math.max(0, Math.min(100, summary.liquidProfitPercentage))}, 100`}
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-3xl font-bold text-gray-900">
                    {formatPercentage(summary.liquidProfitPercentage)}
                  </div>
                  <div className="text-sm text-gray-500">Lucro Líquido</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Cards adicionais */}
        <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
          <StatCard
            title="Economia Mensal"
            value={formatCurrency(summary.monthlySavings || 0)}
            subtitle={`${formatPercentage(summary.savingsRate || 0)} da receita`}
            icon={PiggyBank}
            color="green"
          />

          <StatCard
            title="Próximos Vencimentos"
            value={summary.upcomingDueDates || 0}
            subtitle="Próximos 7 dias"
            icon={Calendar}
            color="yellow"
          />
        </div>
      </div>

      {/* Empréstimos e Dívidas */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
        <div className="flex items-center mb-8">
          <div className="w-14 h-14 bg-gradient-to-br from-red-500 to-rose-600 rounded-2xl flex items-center justify-center mr-5 shadow-lg">
            <CreditCard className="h-7 w-7 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Empréstimos e Dívidas</h2>
            <p className="text-gray-600 mt-1">Controle de empréstimos, faturas e dívidas</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Empréstimos Ativos"
            value={summary.activeLoans || 0}
            subtitle={`${formatCurrency(summary.totalLoansAmount || 0)} total`}
            icon={CreditCard}
            color="yellow"
          />

          <StatCard
            title="Maior Fatura"
            value={formatCurrency(summary.highestBill || 0)}
            subtitle="Cartão com maior valor"
            icon={Calendar}
            color="red"
          />

          <StatCard
            title="Dívidas Totais"
            value={formatCurrency(summary.totalDebts || 0)}
            subtitle="Empréstimos + Faturas"
            icon={TrendingDown}
            color="red"
          />

          <StatCard
            title="Média de Faturas"
            value={formatCurrency(summary.avgBills || 0)}
            subtitle="Média anual"
            icon={CreditCard}
            color="purple"
          />
        </div>
      </div>

      {/* Controle de Gráficos */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Análise Temporal</h2>
        <button
          onClick={swapCharts}
          className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl"
        >
          <Move className="h-4 w-4" />
          Trocar Posição
        </button>
      </div>

      {/* Gráfico de Pizza - Gastos por Categoria */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Gastos por Categoria</h3>
          <button
            onClick={() => navigate('/dashboard-analytics')}
            className="text-sm text-primary-600 hover:text-primary-700 font-medium"
          >
            Ver detalhes →
          </button>
        </div>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={charts.categoryData || []}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={120}
                paddingAngle={2}
                dataKey="value"
                onClick={() => navigate('/dashboard-analytics')}
                className="cursor-pointer"
              >
                {(charts.categoryData || []).map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip
                formatter={(value) => [formatCurrency(value), 'Gasto']}
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #E5E7EB',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 mt-4">
          {(charts.categoryData || []).map((category, index) => (
            <div key={index} className="flex items-center space-x-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: category.color }}
              ></div>
              <span className="text-sm text-gray-600 truncate">
                {category.icon} {category.name}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Gráficos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {chartOrder.map((chartType, index) => {
          if (chartType === 'balance') {
            return (
              <div key="balance" className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Saldo Mensal</h3>
                  <div className="flex items-center text-sm text-gray-500">
                    <GripVertical className="h-4 w-4 mr-1" />
                    {selectedYear || new Date().getFullYear()}
                  </div>
                </div>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={monthlyBalanceData}>
                      <defs>
                        <linearGradient id="balanceGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3}/>
                          <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis
                        dataKey="month"
                        axisLine={false}
                        tickLine={false}
                        tick={{ fontSize: 12, fill: '#6B7280' }}
                      />
                      <YAxis
                        tickFormatter={(value) => formatCurrency(value)}
                        axisLine={false}
                        tickLine={false}
                        tick={{ fontSize: 12, fill: '#6B7280' }}
                      />
                      <Tooltip
                        formatter={(value) => [formatCurrency(value), 'Saldo']}
                        labelStyle={{ color: '#374151' }}
                        contentStyle={{
                          backgroundColor: 'white',
                          border: '1px solid #E5E7EB',
                          borderRadius: '8px',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="balance"
                        stroke="#3B82F6"
                        strokeWidth={3}
                        fill="url(#balanceGradient)"
                        dot={{ fill: '#3B82F6', strokeWidth: 2, r: 5 }}
                        activeDot={{ r: 7, stroke: '#3B82F6', strokeWidth: 2 }}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </div>
            )
          } else {
            return (
              <div key="revenue" className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Receitas vs Despesas</h3>
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-gradient-to-r from-green-400 to-green-600 rounded-full mr-2"></div>
                      <span className="text-gray-600">Receitas</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-gradient-to-r from-red-400 to-red-600 rounded-full mr-2"></div>
                      <span className="text-gray-600">Despesas</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full mr-2"></div>
                      <span className="text-gray-600">Saldo</span>
                    </div>
                  </div>
                </div>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={monthlyRevenueData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                      <defs>
                        <linearGradient id="incomeGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#10B981" stopOpacity={0.9}/>
                          <stop offset="95%" stopColor="#10B981" stopOpacity={0.6}/>
                        </linearGradient>
                        <linearGradient id="expenseGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#EF4444" stopOpacity={0.9}/>
                          <stop offset="95%" stopColor="#EF4444" stopOpacity={0.6}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" vertical={false} />
                      <XAxis
                        dataKey="month"
                        axisLine={false}
                        tickLine={false}
                        tick={{ fontSize: 12, fill: '#6B7280' }}
                      />
                      <YAxis
                        tickFormatter={(value) => {
                          if (value >= 1000) {
                            return `R$ ${(value / 1000).toFixed(0)}k`
                          }
                          return `R$ ${value.toFixed(0)}`
                        }}
                        axisLine={false}
                        tickLine={false}
                        tick={{ fontSize: 12, fill: '#6B7280' }}
                        domain={[0, 'dataMax']}
                      />
                      <Tooltip
                        formatter={(value, name) => [
                          formatCurrency(value),
                          name === 'income' ? 'Receitas' : name === 'expenses' ? 'Despesas' : 'Saldo Líquido'
                        ]}
                        labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                        contentStyle={{
                          backgroundColor: 'white',
                          border: '1px solid #E5E7EB',
                          borderRadius: '12px',
                          boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                          padding: '12px'
                        }}
                      />
                      <Bar
                        dataKey="income"
                        fill="url(#incomeGradient)"
                        radius={[4, 4, 0, 0]}
                        name="income"
                        maxBarSize={60}
                      />
                      <Bar
                        dataKey="expenses"
                        fill="url(#expenseGradient)"
                        radius={[4, 4, 0, 0]}
                        name="expenses"
                        maxBarSize={60}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>

                {/* Resumo mensal */}
                <div className="mt-4 grid grid-cols-3 gap-4 pt-4 border-t border-gray-100">
                  <div className="text-center">
                    <p className="text-sm text-gray-600">Total Receitas</p>
                    <p className="text-lg font-bold text-green-600">
                      {formatCurrency(monthlyRevenueData.reduce((acc, data) => acc + data.income, 0))}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-600">Total Despesas</p>
                    <p className="text-lg font-bold text-red-600">
                      {formatCurrency(monthlyRevenueData.reduce((acc, data) => acc + data.expenses, 0))}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-600">Saldo Líquido</p>
                    <p className={`text-lg font-bold ${
                      monthlyRevenueData.reduce((acc, data) => acc + data.net, 0) >= 0
                        ? 'text-blue-600'
                        : 'text-red-600'
                    }`}>
                      {formatCurrency(monthlyRevenueData.reduce((acc, data) => acc + data.net, 0))}
                    </p>
                  </div>
                </div>
              </div>
            )
          }
        })}
      </div>
    </div>
  )
}

export default Dashboard
