import React, { useState, useEffect } from 'react'
import { CreditCard, Calendar, DollarSign, CheckCircle, AlertCircle, Clock } from 'lucide-react'
import { paymentMethodService } from '../services/bankService'
import toast from 'react-hot-toast'

function BillManager() {
  const [creditCards, setCreditCards] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchCreditCards()
  }, [])

  const fetchCreditCards = async () => {
    try {
      const data = await paymentMethodService.getPaymentMethods()
      // Filtrar apenas cartões de crédito
      const cards = data.filter(method => method.type === 'CREDIT')
      setCreditCards(cards)
    } catch (error) {
      toast.error('Erro ao carregar cartões')
    } finally {
      setLoading(false)
    }
  }

  const handlePayBill = async (cardId) => {
    try {
      await paymentMethodService.payBill(cardId)
      toast.success('Fatura paga com sucesso!')
      fetchCreditCards()
    } catch (error) {
      toast.error(error.response?.data?.error || 'Erro ao pagar fatura')
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('pt-BR')
  }

  const getDaysUntilDue = (dueDate) => {
    const today = new Date()
    const due = new Date(dueDate)
    const diffTime = due - today
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const getBillStatus = (card) => {
    if (card.currentBill === 0) return 'no-bill'
    if (card.isBillPaid) return 'paid'
    
    const daysUntilDue = getDaysUntilDue(card.billDueDate)
    if (daysUntilDue < 0) return 'overdue'
    if (daysUntilDue <= 3) return 'due-soon'
    return 'pending'
  }

  const getStatusConfig = (status) => {
    switch (status) {
      case 'no-bill':
        return {
          color: 'text-gray-500',
          bg: 'bg-gray-100',
          icon: CheckCircle,
          label: 'Sem fatura'
        }
      case 'paid':
        return {
          color: 'text-green-600',
          bg: 'bg-green-100',
          icon: CheckCircle,
          label: 'Paga'
        }
      case 'pending':
        return {
          color: 'text-blue-600',
          bg: 'bg-blue-100',
          icon: Clock,
          label: 'Pendente'
        }
      case 'due-soon':
        return {
          color: 'text-orange-600',
          bg: 'bg-orange-100',
          icon: AlertCircle,
          label: 'Vence em breve'
        }
      case 'overdue':
        return {
          color: 'text-red-600',
          bg: 'bg-red-100',
          icon: AlertCircle,
          label: 'Vencida'
        }
      default:
        return {
          color: 'text-gray-500',
          bg: 'bg-gray-100',
          icon: Clock,
          label: 'Pendente'
        }
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  const totalPending = creditCards
    .filter(card => !card.isBillPaid && card.currentBill > 0)
    .reduce((sum, card) => sum + card.currentBill, 0)

  const overdueCards = creditCards.filter(card => 
    !card.isBillPaid && card.currentBill > 0 && getDaysUntilDue(card.billDueDate) < 0
  )

  const dueSoonCards = creditCards.filter(card => 
    !card.isBillPaid && card.currentBill > 0 && 
    getDaysUntilDue(card.billDueDate) >= 0 && getDaysUntilDue(card.billDueDate) <= 3
  )

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Faturas de Cartão</h1>
          <p className="text-gray-600 mt-1">Gerencie as faturas dos seus cartões de crédito</p>
        </div>
      </div>

      {/* Resumo */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl border shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Pendente</p>
              <p className="text-2xl font-bold text-red-600">
                {formatCurrency(totalPending)}
              </p>
            </div>
            <div className="bg-red-100 p-3 rounded-full">
              <DollarSign className="h-6 w-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl border shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Cartões Ativos</p>
              <p className="text-2xl font-bold text-gray-900">
                {creditCards.length}
              </p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <CreditCard className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl border shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Vencidas</p>
              <p className="text-2xl font-bold text-red-600">
                {overdueCards.length}
              </p>
            </div>
            <div className="bg-red-100 p-3 rounded-full">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl border shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Vencem em 3 dias</p>
              <p className="text-2xl font-bold text-orange-600">
                {dueSoonCards.length}
              </p>
            </div>
            <div className="bg-orange-100 p-3 rounded-full">
              <Clock className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Lista de Cartões */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {creditCards.map((card) => {
          const status = getBillStatus(card)
          const statusConfig = getStatusConfig(status)
          const StatusIcon = statusConfig.icon
          const daysUntilDue = getDaysUntilDue(card.billDueDate)

          return (
            <div
              key={card.id}
              className="bg-white rounded-2xl border shadow-sm p-6 transition-all hover:shadow-md"
            >
              <div className="flex items-start justify-between mb-6">
                <div className="flex items-center gap-4">
                  <div
                    className="w-14 h-14 rounded-2xl flex items-center justify-center text-2xl shadow-sm"
                    style={{ backgroundColor: card.color + '15', color: card.color, border: `2px solid ${card.color}20` }}
                  >
                    {card.icon}
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900 text-lg">{card.name}</h3>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-sm text-gray-500">
                        {card.bank?.icon} {card.bank?.name}
                      </span>
                    </div>
                  </div>
                </div>
                <div className={`flex items-center gap-1 px-2 py-1 rounded-lg ${statusConfig.bg}`}>
                  <StatusIcon className={`h-4 w-4 ${statusConfig.color}`} />
                  <span className={`text-xs font-medium ${statusConfig.color}`}>
                    {statusConfig.label}
                  </span>
                </div>
              </div>

              <div className="space-y-4">
                {/* Valor da Fatura */}
                <div className="bg-gray-50 rounded-xl p-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-600">Valor da Fatura</span>
                    <span className={`text-xl font-bold ${
                      card.currentBill > 0 ? 'text-red-600' : 'text-gray-400'
                    }`}>
                      {formatCurrency(card.currentBill)}
                    </span>
                  </div>
                </div>

                {/* Informações de Vencimento */}
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2 text-gray-600">
                    <Calendar className="h-4 w-4" />
                    <span>Vencimento: dia {card.billDueDay}</span>
                  </div>
                  {card.billDueDate && card.currentBill > 0 && (
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      daysUntilDue < 0 ? 'bg-red-100 text-red-700' :
                      daysUntilDue <= 3 ? 'bg-orange-100 text-orange-700' :
                      'bg-blue-100 text-blue-700'
                    }`}>
                      {daysUntilDue < 0 ? `${Math.abs(daysUntilDue)} dias em atraso` :
                       daysUntilDue === 0 ? 'Vence hoje' :
                       `${daysUntilDue} dias`}
                    </span>
                  )}
                </div>

                {/* Próxima Data de Vencimento */}
                {card.billDueDate && (
                  <div className="text-sm text-gray-600">
                    <span>Próximo vencimento: {formatDate(card.billDueDate)}</span>
                  </div>
                )}

                {/* Botão de Pagamento */}
                {card.currentBill > 0 && !card.isBillPaid && (
                  <button
                    onClick={() => handlePayBill(card.id)}
                    className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
                  >
                    <CheckCircle className="h-4 w-4" />
                    <span>Pagar Fatura</span>
                  </button>
                )}

                {card.currentBill === 0 && (
                  <div className="text-center py-3 text-gray-500 text-sm">
                    Nenhuma fatura pendente
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>

      {creditCards.length === 0 && (
        <div className="text-center py-12">
          <CreditCard className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum cartão de crédito cadastrado</h3>
          <p className="text-gray-600">Cadastre cartões de crédito na seção de Formas de Pagamento</p>
        </div>
      )}
    </div>
  )
}

export default BillManager
