import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'react-hot-toast'
import { FileText, Tag, X, Palette } from 'lucide-react'
import api from '../services/api'

function TagModal({ isOpen, onClose, tag, onSuccess }) {
  const [loading, setLoading] = useState(false)
  const { register, handleSubmit, reset, setValue, watch, formState: { errors } } = useForm()

  useEffect(() => {
    if (isOpen) {
      if (tag) {
        // Modo edição
        reset({
          name: tag.name,
          color: tag.color
        })
      } else {
        // Modo criação - manter valores existentes
        const currentValues = {
          name: watch('name') || '',
          color: watch('color') || '#3B82F6'
        }
        reset(currentValues)
      }
    }
  }, [isOpen, tag])

  const handleColorChange = (color) => {
    const currentValues = watch()
    setValue('color', color, { shouldDirty: true })
    if (currentValues.name) {
      setValue('name', currentValues.name, { shouldDirty: true })
    }
  }

  const onSubmit = async (data) => {
    try {
      setLoading(true)

      if (tag) {
        await api.put(`/tags/${tag.id}`, data)
        toast.success('Tag atualizada com sucesso!')
      } else {
        await api.post('/tags', data)
        toast.success('Tag criada com sucesso!')
      }

      onSuccess()
      onClose()
    } catch (error) {
      console.error('Erro ao salvar tag:', error)
      toast.error('Erro ao salvar tag')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md transform transition-all">
        {/* Header */}
        <div 
          className="bg-gradient-to-r from-slate-700 to-slate-800 text-white p-6 rounded-t-2xl"
          style={{ backgroundColor: watch('color') }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-white/10 backdrop-blur rounded-xl flex items-center justify-center text-2xl shadow-lg">
                <Tag className="h-6 w-6" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">
                  {tag ? 'Editar Tag' : 'Nova Tag'}
                </h2>
                <p className="text-slate-300 text-sm mt-1">
                  {tag ? 'Altere os dados da tag' : 'Crie uma nova tag para suas transações'}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
          {/* Nome */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nome *
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                {...register('name', { required: 'Nome é obrigatório' })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent transition-all"
                placeholder="Ex: Viagem, Presente, Emergência"
              />
            </div>
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          {/* Cor */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
              <Palette className="h-4 w-4" />
              Cor
            </label>
            <div className="relative">
              <input
                type="color"
                value={watch('color')}
                onChange={(e) => handleColorChange(e.target.value)}
                className="w-full h-12 rounded-xl cursor-pointer border border-gray-300 p-1"
              />
            </div>
          </div>

          {/* Botões */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-3 text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition-colors font-medium"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 px-4 py-3 bg-gradient-to-r from-slate-700 to-slate-800 text-white rounded-xl hover:from-slate-800 hover:to-slate-900 transition-all font-medium disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
            >
              {loading ? 'Salvando...' : tag ? 'Salvar' : 'Criar'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default TagModal 