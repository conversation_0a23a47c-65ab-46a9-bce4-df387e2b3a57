import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Filter, Search, Upload, Eye, Download, Calendar, DollarSign, Tag, CreditCard, FileText, Building, X } from 'lucide-react'
import { useForm } from 'react-hook-form'
import toast from 'react-hot-toast'
import api from '../services/api'
import { bankService, paymentMethodService } from '../services/bankService'
import CurrencyInput from '../components/CurrencyInput'
import ReceiptModal from '../components/ReceiptModal'
import InstallmentModal from '../components/InstallmentModal'
import TransactionDetailModal from '../components/TransactionDetailModal'
import { useFilters } from '../contexts/FilterContext'

function Transactions() {
  const { transactionFilters, updateTransactionFilters, clearTransactionFilters } = useFilters()
  const [transactions, setTransactions] = useState([])
  const [categories, setCategories] = useState([])
  const [tags, setTags] = useState([])
  const [banks, setBanks] = useState([])
  const [paymentMethods, setPaymentMethods] = useState([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingTransaction, setEditingTransaction] = useState(null)
  const [transactionAmount, setTransactionAmount] = useState(0)
  const [selectedFile, setSelectedFile] = useState(null)
  const [showFilters, setShowFilters] = useState(false)
  const [installments, setInstallments] = useState(1)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  const [showReceiptModal, setShowReceiptModal] = useState(false)
  const [selectedReceipt, setSelectedReceipt] = useState(null)
  const [showInstallmentModal, setShowInstallmentModal] = useState(false)
  const [selectedInstallment, setSelectedInstallment] = useState(null)
  const [installmentDetails, setInstallmentDetails] = useState([])
  const [showTransactionDetailModal, setShowTransactionDetailModal] = useState(false)
  const [selectedTransactionDetail, setSelectedTransactionDetail] = useState(null)
  const [selectedTags, setSelectedTags] = useState([])
  const [showTagDropdown, setShowTagDropdown] = useState(false)

  // Resetar página quando filtros mudarem
  useEffect(() => {
    setCurrentPage(1)
  }, [transactionFilters])

  const { register, handleSubmit, reset, watch, formState: { errors } } = useForm()
  const selectedBankId = watch('bankId')

  useEffect(() => {
    fetchTransactions()
    fetchCategories()
    fetchTags()
    fetchBanks()
    fetchPaymentMethods()
  }, [transactionFilters])

  // Fechar dropdown de tags quando clicar fora
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showTagDropdown && !event.target.closest('.tag-dropdown-container')) {
        setShowTagDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [showTagDropdown])

  const fetchTags = async () => {
    try {
      const response = await api.get('/tags')
      setTags(response.data)
    } catch (error) {
      console.error('Erro ao buscar tags:', error)
    }
  }

  const fetchBanks = async () => {
    try {
      const data = await bankService.getBanks()
      setBanks(data)
    } catch (error) {
      console.error('Erro ao carregar bancos:', error)
    }
  }

  const fetchPaymentMethods = async () => {
    try {
      const data = await paymentMethodService.getPaymentMethods()
      setPaymentMethods(data)
    } catch (error) {
      console.error('Erro ao carregar formas de pagamento:', error)
    }
  }

  // Filtrar métodos de pagamento baseado no banco selecionado
  const getFilteredPaymentMethods = (selectedBankId) => {
    if (!selectedBankId) {
      // Se nenhum banco selecionado, mostrar todos
      return paymentMethods
    }

    return paymentMethods.filter(method =>
      method.bankId === selectedBankId || !method.bankId // Métodos do banco selecionado ou sem banco
    )
  }

  const fetchTransactions = async () => {
    try {
      setLoading(true)
      const response = await api.get('/transactions')

      // Backend já retorna ordenado por data decrescente
      setTransactions(response.data)
    } catch (error) {
      console.error('Erro ao buscar transações:', error)
      toast.error('Erro ao carregar transações')
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await api.get('/categories')
      setCategories(response.data)
    } catch (error) {
      console.error('Erro ao buscar categorias:', error)
    }
  }

  const onSubmit = async (data) => {
    try {
      const formData = new FormData()

      // Adicionar dados da transação
      formData.append('description', data.description)
      formData.append('amount', transactionAmount)
      formData.append('type', data.type)
      formData.append('date', data.date)
      formData.append('installments', installments)

      if (data.categoryId) formData.append('categoryId', data.categoryId)
      if (data.bankId) formData.append('bankId', data.bankId)
      if (data.paymentMethodId) formData.append('paymentMethodId', data.paymentMethodId)
      if (selectedTags.length > 0) formData.append('tagIds', JSON.stringify(selectedTags))

      // Adicionar arquivo se selecionado
      if (selectedFile) {
        formData.append('receipt', selectedFile)
      }

      if (editingTransaction) {
        // Para edição, usar JSON normal (sem arquivo por enquanto)
        const transactionData = {
          ...data,
          amount: transactionAmount,
          bankId: data.bankId || null,
          paymentMethodId: data.paymentMethodId || null,
          tagIds: selectedTags
        }
        await api.put(`/transactions/${editingTransaction.id}`, transactionData)
        toast.success('Transação atualizada com sucesso!')
      } else {
        await api.post('/transactions', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        toast.success('Transação criada com sucesso!')
      }

      setShowModal(false)
      setEditingTransaction(null)
      setTransactionAmount(0)
      setSelectedFile(null)
      setInstallments(1)
      setSelectedPaymentMethod(null)
      setSelectedTags([])
      reset()
      fetchTransactions()
      fetchBanks() // Atualizar saldos dos bancos
    } catch (error) {
      console.error('Erro ao salvar transação:', error)
      toast.error(error.response?.data?.error || 'Erro ao salvar transação')
    }
  }

  const handleEdit = (transaction) => {
    setEditingTransaction(transaction)
    setTransactionAmount(transaction.amount)
    setSelectedTags(transaction.tags?.map(tag => tag.id) || [])
    reset({
      description: transaction.description,
      type: transaction.type,
      categoryId: transaction.categoryId || '',
      bankId: transaction.bankId || '',
      paymentMethodId: transaction.paymentMethodId || '',
      date: transaction.date.split('T')[0]
    })
    setShowModal(true)
  }

  const handleDelete = async (id) => {
    if (window.confirm('Tem certeza que deseja deletar esta transação?')) {
      try {
        await api.delete(`/transactions/${id}`)
        toast.success('Transação deletada com sucesso!')
        fetchTransactions()
      } catch (error) {
        console.error('Erro ao deletar transação:', error)
        toast.error('Erro ao deletar transação')
      }
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const handleViewReceipt = (receiptUrl, description) => {
    setSelectedReceipt({ url: receiptUrl, description })
    setShowReceiptModal(true)
  }

  const handleViewInstallment = async (transaction) => {
    try {
      // Buscar detalhes das parcelas
      const response = await api.get(`/transactions/${transaction.id}/installments`)
      setSelectedInstallment(transaction)
      setInstallmentDetails(response.data)
      setShowInstallmentModal(true)
    } catch (error) {
      console.error('Erro ao buscar detalhes das parcelas:', error)
      toast.error('Erro ao carregar detalhes das parcelas')
    }
  }

  const handleViewTransactionDetail = (transaction) => {
    setSelectedTransactionDetail(transaction)
    setShowTransactionDetailModal(true)
  }

  const getTypeColor = (type) => {
    switch (type) {
      case 'INCOME': return 'text-green-600 bg-green-100'
      case 'EXPENSE': return 'text-red-600 bg-red-100'
      case 'INVESTMENT': return 'text-blue-600 bg-blue-100'
      case 'LOAN': return 'text-yellow-600 bg-yellow-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getTypeLabel = (type) => {
    switch (type) {
      case 'INCOME': return 'Receita'
      case 'EXPENSE': return 'Despesa'
      case 'INVESTMENT': return 'Investimento'
      case 'LOAN': return 'Empréstimo'
      default: return type
    }
  }

  // Aplicar filtros e ordenação
  const filteredTransactions = transactions.filter(transaction => {
    // Filtro por busca (descrição)
    if (transactionFilters.search && transactionFilters.search.trim() !== '') {
      const searchTerm = transactionFilters.search.toLowerCase().trim()
      if (!transaction.description?.toLowerCase().includes(searchTerm)) {
        return false
      }
    }

    // Filtro por tipo
    if (transactionFilters.type && transactionFilters.type !== '') {
      if (transaction.type !== transactionFilters.type) {
        return false
      }
    }

    // Filtro por categoria
    if (transactionFilters.category && transactionFilters.category !== '') {
      if (transaction.categoryId !== transactionFilters.category) {
        return false
      }
    }

    // Filtro por tag
    if (transactionFilters.tag && transactionFilters.tag !== '') {
      if (!transaction.tags?.some(tag => tag.id === transactionFilters.tag)) {
        return false
      }
    }

    // Filtro por banco
    if (transactionFilters.bank && transactionFilters.bank !== '') {
      if (transaction.bankId !== transactionFilters.bank) {
        return false
      }
    }

    // Filtro por data de início
    if (transactionFilters.dateFrom && transactionFilters.dateFrom !== '') {
      const transactionDate = new Date(transaction.date)
      const fromDate = new Date(transactionFilters.dateFrom)
      fromDate.setHours(0, 0, 0, 0)
      transactionDate.setHours(0, 0, 0, 0)
      if (transactionDate < fromDate) {
        return false
      }
    }

    // Filtro por data final
    if (transactionFilters.dateTo && transactionFilters.dateTo !== '') {
      const transactionDate = new Date(transaction.date)
      const toDate = new Date(transactionFilters.dateTo)
      transactionDate.setHours(0, 0, 0, 0)
      toDate.setHours(23, 59, 59, 999)
      if (transactionDate > toDate) {
        return false
      }
    }

    // Filtro por valor mínimo
    if (transactionFilters.amountMin && transactionFilters.amountMin !== '') {
      const minAmount = parseFloat(transactionFilters.amountMin)
      if (!isNaN(minAmount) && transaction.amount < minAmount) {
        return false
      }
    }

    // Filtro por valor máximo
    if (transactionFilters.amountMax && transactionFilters.amountMax !== '') {
      const maxAmount = parseFloat(transactionFilters.amountMax)
      if (!isNaN(maxAmount) && transaction.amount > maxAmount) {
        return false
      }
    }

    // Filtro por forma de pagamento
    if (transactionFilters.paymentMethod && transactionFilters.paymentMethod !== '') {
      if (transaction.paymentMethodId !== transactionFilters.paymentMethod) {
        return false
      }
    }

    // Filtro por comprovante
    if (transactionFilters.hasReceipt && transactionFilters.hasReceipt !== '') {
      if (transactionFilters.hasReceipt === 'true' && !transaction.receiptUrl) {
        return false
      }
      if (transactionFilters.hasReceipt === 'false' && transaction.receiptUrl) {
        return false
      }
    }

    return true
  })
  // Backend já retorna ordenado por data decrescente, mantemos a ordem

  // Aplicar paginação
  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedTransactions = filteredTransactions.slice(startIndex, endIndex)

  return (
    <div className="space-y-8 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Transações</h1>
          <p className="text-gray-600 mt-1">Gerencie suas receitas e despesas com controle total</p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              showFilters
                ? 'bg-blue-100 text-blue-700 border border-blue-200'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
            }`}
          >
            <Filter className="h-4 w-4" />
            <span>Filtros</span>
          </button>
          <button
            onClick={() => {
              setEditingTransaction(null)
              setTransactionAmount(0)
              setSelectedFile(null)
              reset()
              setShowModal(true)
            }}
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-slate-900 rounded-lg hover:bg-slate-800 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Nova Transação</span>
          </button>
        </div>
      </div>

      {/* Filtros Avançados */}
      {showFilters && (
        <div className="bg-white rounded-2xl border shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Filtros Avançados</h3>
            <button
              onClick={clearTransactionFilters}
              className="text-sm text-gray-600 hover:text-gray-800 transition-colors"
            >
              Limpar Todos
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {/* Busca */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Search className="inline h-4 w-4 mr-1" />
                Buscar
              </label>
              <input
                type="text"
                placeholder="Descrição da transação..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={transactionFilters.search}
                onChange={(e) => updateTransactionFilters({ search: e.target.value })}
              />
            </div>

            {/* Tipo */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Tag className="inline h-4 w-4 mr-1" />
                Tipo
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={transactionFilters.type}
                onChange={(e) => updateTransactionFilters({ type: e.target.value })}
              >
                <option value="">Todos os tipos</option>
                <option value="INCOME">💰 Receita</option>
                <option value="EXPENSE">💸 Despesa</option>
                <option value="INVESTMENT">📈 Investimento</option>
              </select>
            </div>

            {/* Categoria */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Categoria</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={transactionFilters.category}
                onChange={(e) => updateTransactionFilters({ category: e.target.value })}
              >
                <option value="">Todas as categorias</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.icon} {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Banco */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Banco</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={transactionFilters.bank}
                onChange={(e) => updateTransactionFilters({ bank: e.target.value })}
              >
                <option value="">Todos os bancos</option>
                {banks.map((bank) => (
                  <option key={bank.id} value={bank.id}>
                    {bank.icon} {bank.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Data De */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Data De
              </label>
              <input
                type="date"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={transactionFilters.dateFrom}
                onChange={(e) => updateTransactionFilters({ dateFrom: e.target.value })}
              />
            </div>

            {/* Data Até */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Data Até
              </label>
              <input
                type="date"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={transactionFilters.dateTo}
                onChange={(e) => updateTransactionFilters({ dateTo: e.target.value })}
              />
            </div>

            {/* Valor Mínimo */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <DollarSign className="inline h-4 w-4 mr-1" />
                Valor Mínimo
              </label>
              <input
                type="number"
                step="0.01"
                placeholder="0,00"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={transactionFilters.amountMin || ''}
                onChange={(e) => updateTransactionFilters({ amountMin: e.target.value })}
              />
            </div>

            {/* Valor Máximo */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <DollarSign className="inline h-4 w-4 mr-1" />
                Valor Máximo
              </label>
              <input
                type="number"
                step="0.01"
                placeholder="0,00"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={transactionFilters.amountMax || ''}
                onChange={(e) => updateTransactionFilters({ amountMax: e.target.value })}
              />
            </div>

            {/* Forma de Pagamento */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Forma de Pagamento</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={transactionFilters.paymentMethod || ''}
                onChange={(e) => updateTransactionFilters({ paymentMethod: e.target.value })}
              >
                <option value="">Todas as formas</option>
                {paymentMethods.map((method) => (
                  <option key={method.id} value={method.id}>
                    {method.icon} {method.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Comprovante */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Comprovante</label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={transactionFilters.hasReceipt || ''}
                onChange={(e) => updateTransactionFilters({ hasReceipt: e.target.value })}
              >
                <option value="">Todos</option>
                <option value="true">Com comprovante</option>
                <option value="false">Sem comprovante</option>
              </select>
            </div>

            {/* Tag */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Tag className="inline h-4 w-4 mr-1" />
                Tag
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={transactionFilters.tag}
                onChange={(e) => updateTransactionFilters({ tag: e.target.value })}
              >
                <option value="">Todas as tags</option>
                {tags.map((tag) => (
                  <option key={tag.id} value={tag.id}>
                    # {tag.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Lista de Transações */}
      <div className="bg-white rounded-2xl border shadow-lg overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : paginatedTransactions.length === 0 ? (
          <div className="text-center py-12">
            <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
              <Search className="h-12 w-12" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhuma transação encontrada</h3>
            <p className="text-gray-600">Tente ajustar os filtros ou criar uma nova transação</p>
          </div>
        ) : (
          <div className="relative">
            {/* Container da tabela responsivo */}
            <div className="w-full overflow-x-auto">
              <div className="max-h-[70vh] overflow-y-auto">
                <table className="min-w-full divide-y divide-gray-200 relative" style={{ minWidth: '1200px' }}>
                  {/* Header fixo com design melhorado e larguras definidas */}
                  <thead className="bg-gradient-to-r from-gray-50 to-gray-100 sticky top-0 z-10 shadow-sm">
                    <tr className="border-b-2 border-gray-200">
                      <th className="px-4 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider bg-gradient-to-r from-gray-50 to-gray-100" style={{ width: '25%' }}>
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          Transação
                        </div>
                      </th>
                      <th className="px-3 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider bg-gradient-to-r from-gray-50 to-gray-100" style={{ width: '12%' }}>
                        <div className="flex items-center gap-2">
                          <Tag className="h-4 w-4" />
                          Categoria
                        </div>
                      </th>
                      <th className="px-3 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider bg-gradient-to-r from-gray-50 to-gray-100" style={{ width: '10%' }}>
                        <div className="flex items-center gap-2">
                          <Tag className="h-4 w-4" />
                          Tags
                        </div>
                      </th>
                      <th className="px-3 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider bg-gradient-to-r from-gray-50 to-gray-100" style={{ width: '15%' }}>
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4" />
                          Banco/Pagamento
                        </div>
                      </th>
                      <th className="px-3 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider bg-gradient-to-r from-gray-50 to-gray-100" style={{ width: '12%' }}>
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4" />
                          Valor
                        </div>
                      </th>
                      <th className="px-3 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider bg-gradient-to-r from-gray-50 to-gray-100" style={{ width: '10%' }}>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          Data
                        </div>
                      </th>
                      <th className="px-3 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider bg-gradient-to-r from-gray-50 to-gray-100" style={{ width: '8%' }}>
                        <div className="flex items-center gap-2">
                          <Eye className="h-4 w-4" />
                          Comprovante
                        </div>
                      </th>
                      <th className="px-3 py-5 text-left text-xs font-bold text-gray-700 uppercase tracking-wider bg-gradient-to-r from-gray-50 to-gray-100" style={{ width: '8%' }}>
                        <div className="flex items-center gap-2">
                          <Edit className="h-4 w-4" />
                          Ações
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-100">
                    {paginatedTransactions.map((transaction, index) => (
                      <tr
                        key={transaction.id}
                        className={`
                          hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50
                          transition-all duration-200 cursor-pointer border-l-4 border-transparent
                          hover:border-l-blue-400 hover:shadow-sm
                          ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/30'}
                        `}
                        onClick={() => handleViewTransactionDetail(transaction)}
                      >
                        <td className="px-4 py-5" style={{ width: '25%' }}>
                          <div className="flex items-center">
                            <div className={`w-10 h-10 rounded-xl flex items-center justify-center text-white text-sm font-bold mr-3 shadow-sm ${
                              transaction.type === 'INCOME' ? 'bg-gradient-to-br from-green-500 to-green-600' :
                              transaction.type === 'EXPENSE' ? 'bg-gradient-to-br from-red-500 to-red-600' :
                              transaction.type === 'INVESTMENT' ? 'bg-gradient-to-br from-blue-500 to-blue-600' : 'bg-gradient-to-br from-gray-500 to-gray-600'
                            }`}>
                              {transaction.type === 'INCOME' ? '↗' :
                               transaction.type === 'EXPENSE' ? '↙' :
                               transaction.type === 'INVESTMENT' ? '📈' : '🏦'}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2">
                                <div className="text-sm font-semibold text-gray-900 leading-tight truncate">
                                  {transaction.description}
                                </div>
                            {transaction.installments > 1 && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleViewInstallment(transaction)
                                }}
                                className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors flex-shrink-0"
                                title="Ver detalhes do parcelamento"
                              >
                                <CreditCard className="h-3 w-3" />
                                {transaction.installments}x
                              </button>
                            )}
                          </div>
                              <div className="text-xs text-gray-600 mt-1 truncate">
                                {getTypeLabel(transaction.type)}
                                {transaction.installments > 1 && (
                                  <span className="ml-2 text-blue-600 font-medium">
                                    • Parcelado em {transaction.installments}x
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-3 py-5" style={{ width: '12%' }}>
                          {transaction.category ? (
                            <div className="flex items-center">
                              <div className="w-6 h-6 rounded-lg bg-gray-100 flex items-center justify-center mr-2">
                                <span className="text-sm">{transaction.category.icon}</span>
                              </div>
                              <span className="text-sm font-medium text-gray-900 truncate">{transaction.category.name}</span>
                            </div>
                          ) : (
                            <div className="flex items-center">
                              <div className="w-6 h-6 rounded-lg bg-gray-100 flex items-center justify-center mr-2">
                                <span className="text-gray-400">—</span>
                              </div>
                              <span className="text-sm text-gray-400">Sem categoria</span>
                            </div>
                          )}
                        </td>
                        <td className="px-3 py-5" style={{ width: '10%' }}>
                          <div className="flex flex-wrap gap-1">
                            {transaction.tags && transaction.tags.length > 0 ? (
                              transaction.tags.slice(0, 2).map((tag) => (
                                <div
                                  key={tag.id}
                                  className="inline-flex items-center gap-1 px-1.5 py-0.5 rounded-full text-xs"
                                  style={{ backgroundColor: tag.color, color: '#FFF' }}
                                  title={tag.name}
                                >
                                  <Tag className="h-2 w-2" />
                                  <span className="truncate max-w-[40px]">{tag.name}</span>
                                </div>
                              ))
                            ) : (
                              <span className="text-sm text-gray-400">—</span>
                            )}
                            {transaction.tags && transaction.tags.length > 2 && (
                              <span className="text-xs text-gray-500">+{transaction.tags.length - 2}</span>
                            )}
                          </div>
                        </td>
                        <td className="px-3 py-5" style={{ width: '15%' }}>
                          <div className="space-y-1">
                            {transaction.bank && (
                              <div className="flex items-center">
                                <div className="w-5 h-5 rounded bg-blue-100 flex items-center justify-center mr-1">
                                  <span className="text-xs">{transaction.bank.icon}</span>
                                </div>
                                <span className="text-xs font-medium text-gray-900 truncate">{transaction.bank.name}</span>
                              </div>
                            )}
                            {transaction.paymentMethod && (
                              <div className="flex items-center">
                                <div className="w-5 h-5 rounded bg-purple-100 flex items-center justify-center mr-1">
                                  <span className="text-xs">{transaction.paymentMethod.icon}</span>
                                </div>
                                <span className="text-xs text-gray-600 truncate">{transaction.paymentMethod.name}</span>
                              </div>
                            )}
                            {!transaction.bank && !transaction.paymentMethod && (
                              <div className="flex items-center">
                                <div className="w-5 h-5 rounded bg-gray-100 flex items-center justify-center mr-1">
                                  <span className="text-gray-400 text-xs">—</span>
                                </div>
                                <span className="text-xs text-gray-400">Não informado</span>
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-3 py-5" style={{ width: '12%' }}>
                          <div className={`inline-flex items-center px-2 py-1 rounded-lg text-xs font-bold whitespace-nowrap ${
                            transaction.type === 'INCOME'
                              ? 'bg-green-100 text-green-700 border border-green-200'
                              : 'bg-red-100 text-red-700 border border-red-200'
                          }`}>
                            <span className="mr-1">
                              {transaction.type === 'INCOME' ? '↗' : '↙'}
                            </span>
                            <span>
                              {transaction.type === 'INCOME' ? '+' : '-'}
                              {new Intl.NumberFormat('pt-BR', {
                                style: 'currency',
                                currency: 'BRL'
                              }).format(transaction.amount)}
                            </span>
                          </div>
                        </td>
                        <td className="px-3 py-5" style={{ width: '10%' }}>
                          <div className="text-xs">
                            <div className="font-medium text-gray-900">
                              {new Date(transaction.date).toLocaleDateString('pt-BR')}
                            </div>
                            <div className="text-xs text-gray-500">
                              {new Date(transaction.date).toLocaleTimeString('pt-BR', {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </div>
                          </div>
                        </td>
                        <td className="px-3 py-5" style={{ width: '8%' }}>
                          {transaction.receiptUrl ? (
                            <div className="flex flex-col items-center gap-1">
                              {/* Thumbnail compacto */}
                              <div className="w-8 h-8 rounded border border-gray-200 overflow-hidden bg-gray-50">
                                {transaction.receiptUrl.includes('image') || transaction.receiptUrl.match(/\.(jpg|jpeg|png|gif|webp)$/i) ? (
                                  <img
                                    src={transaction.receiptUrl}
                                    alt="Comprovante"
                                    className="w-full h-full object-cover"
                                    onError={(e) => {
                                      e.target.style.display = 'none'
                                      e.target.nextSibling.style.display = 'flex'
                                    }}
                                  />
                                ) : (
                                  <div className="w-full h-full flex items-center justify-center text-gray-400 text-sm">
                                    📄
                                  </div>
                                )}
                                <div className="w-full h-full hidden items-center justify-center text-gray-400 text-sm">
                                  📄
                                </div>
                              </div>

                              {/* Botão Ver compacto */}
                              <button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleViewReceipt(transaction.receiptUrl, transaction.description)
                                }}
                                className="flex items-center gap-1 px-1 py-0.5 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                                title="Visualizar comprovante"
                              >
                                <Eye className="h-2 w-2" />
                                Ver
                              </button>
                            </div>
                          ) : (
                            <div className="flex flex-col items-center">
                              <div className="w-8 h-8 rounded border-2 border-dashed border-gray-300 flex items-center justify-center">
                                <span className="text-gray-400 text-sm">📄</span>
                              </div>
                              <span className="text-xs text-gray-400">—</span>
                            </div>
                          )}
                        </td>
                        <td className="px-3 py-5" style={{ width: '8%' }}>
                          <div className="flex items-center gap-1">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleEdit(transaction)
                              }}
                              className="p-1.5 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors border border-gray-200 hover:border-blue-300"
                              title="Editar"
                            >
                              <Edit className="h-3 w-3" />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleDelete(transaction.id)
                              }}
                              className="p-1.5 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors border border-gray-200 hover:border-red-300"
                              title="Excluir"
                            >
                              <Trash2 className="h-3 w-3" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Paginação */}
        {filteredTransactions.length > itemsPerPage && (
          <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200">
            <div className="text-sm text-gray-700">
              Mostrando {startIndex + 1} a {Math.min(endIndex, filteredTransactions.length)} de {filteredTransactions.length} transações
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Anterior
              </button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum
                  if (totalPages <= 5) {
                    pageNum = i + 1
                  } else if (currentPage <= 3) {
                    pageNum = i + 1
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i
                  } else {
                    pageNum = currentPage - 2 + i
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`px-3 py-1 text-sm border rounded-lg ${
                        currentPage === pageNum
                          ? 'bg-blue-600 text-white border-blue-600'
                          : 'border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  )
                })}
              </div>

              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Próxima
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              {editingTransaction ? 'Editar Transação' : 'Nova Transação'}
            </h2>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Descrição
                </label>
                <input
                  {...register('description', { required: 'Descrição é obrigatória' })}
                  type="text"
                  className="input-field"
                  placeholder="Descrição da transação"
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Valor
                </label>
                <CurrencyInput
                  value={transactionAmount}
                  onChange={setTransactionAmount}
                  className="input-field"
                  placeholder="R$ 0,00"
                />
                {transactionAmount <= 0 && (
                  <p className="mt-1 text-sm text-red-600">Valor deve ser maior que zero</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tipo
                </label>
                <select
                  {...register('type', { required: 'Tipo é obrigatório' })}
                  className="input-field"
                >
                  <option value="">Selecione o tipo</option>
                  <option value="INCOME">Receita</option>
                  <option value="EXPENSE">Despesa</option>
                  <option value="INVESTMENT">Investimento</option>
                </select>
                {errors.type && (
                  <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Categoria
                </label>
                <select
                  {...register('categoryId')}
                  className="input-field"
                >
                  <option value="">Selecione uma categoria</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Banco
                </label>
                <select
                  {...register('bankId')}
                  className="input-field"
                >
                  <option value="">Selecione um banco</option>
                  {banks.map((bank) => (
                    <option key={bank.id} value={bank.id}>
                      {bank.icon} {bank.name} - {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(bank.currentBalance)}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Forma de Pagamento
                </label>
                <select
                  {...register('paymentMethodId')}
                  className="input-field"
                  onChange={(e) => {
                    const selectedMethod = getFilteredPaymentMethods(selectedBankId).find(m => m.id === e.target.value)
                    setSelectedPaymentMethod(selectedMethod)
                  }}
                >
                  <option value="">Selecione uma forma de pagamento</option>
                  {getFilteredPaymentMethods(selectedBankId).map((method) => (
                    <option key={method.id} value={method.id}>
                      {method.icon} {method.name}
                      {method.bank && ` (${method.bank.name})`}
                    </option>
                  ))}
                </select>
                {selectedBankId && (
                  <p className="mt-1 text-xs text-blue-600">
                    💡 Mostrando apenas métodos do banco selecionado e métodos sem banco
                  </p>
                )}
              </div>

              {/* Parcelamento - apenas para cartões de crédito */}
              {selectedPaymentMethod?.type === 'CREDIT' && !editingTransaction && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Parcelamento
                  </label>
                  <select
                    value={installments}
                    onChange={(e) => setInstallments(parseInt(e.target.value))}
                    className="input-field"
                  >
                    {Array.from({ length: 12 }, (_, i) => i + 1).map(num => (
                      <option key={num} value={num}>
                        {num}x {num > 1 && `de ${new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(transactionAmount / num)}`}
                      </option>
                    ))}
                  </select>
                  {installments > 1 && (
                    <p className="mt-1 text-sm text-blue-600">
                      💳 Será cobrado {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(transactionAmount / installments)} por mês durante {installments} meses
                    </p>
                  )}
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Data
                </label>
                <input
                  {...register('date')}
                  type="date"
                  className="input-field"
                  defaultValue={new Date().toISOString().split('T')[0]}
                />
              </div>

              {/* Upload de Comprovante */}
              {!editingTransaction && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Upload className="inline h-4 w-4 mr-1" />
                    Comprovante (Opcional)
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
                    <input
                      type="file"
                      accept="image/*,.pdf"
                      onChange={(e) => setSelectedFile(e.target.files[0])}
                      className="hidden"
                      id="receipt-upload"
                    />
                    <label htmlFor="receipt-upload" className="cursor-pointer">
                      {selectedFile ? (
                        <div className="flex items-center justify-center gap-2 text-green-600">
                          <Upload className="h-5 w-5" />
                          <span className="text-sm font-medium">{selectedFile.name}</span>
                        </div>
                      ) : (
                        <div className="text-gray-500">
                          <Upload className="h-8 w-8 mx-auto mb-2" />
                          <p className="text-sm">Clique para enviar uma foto ou PDF</p>
                          <p className="text-xs text-gray-400 mt-1">PNG, JPG ou PDF até 10MB</p>
                        </div>
                      )}
                    </label>
                  </div>
                  {selectedFile && (
                    <button
                      type="button"
                      onClick={() => setSelectedFile(null)}
                      className="mt-2 text-sm text-red-600 hover:text-red-800"
                    >
                      Remover arquivo
                    </button>
                  )}
                </div>
              )}

              {/* Tags */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tags
                </label>
                <div className="relative tag-dropdown-container">
                  <div className="min-h-[42px] p-1.5 border border-gray-300 rounded-lg focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
                    <div className="flex flex-wrap gap-1.5">
                      {selectedTags.map(tagId => {
                        const tag = tags.find(t => t.id === tagId);
                        if (!tag) return null;
                        return (
                          <div
                            key={tag.id}
                            className="inline-flex items-center gap-1 px-2 py-1 rounded-md text-sm"
                            style={{ backgroundColor: tag.color, color: '#FFF' }}
                          >
                            <span>{tag.name}</span>
                            <button
                              type="button"
                              onClick={() => setSelectedTags(selectedTags.filter(id => id !== tag.id))}
                              className="hover:bg-white hover:bg-opacity-20 rounded-full p-0.5"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </div>
                        );
                      })}
                      <input
                        type="text"
                        className="flex-1 min-w-[120px] outline-none bg-transparent text-sm"
                        placeholder={selectedTags.length === 0 ? "Selecione as tags..." : ""}
                        onFocus={() => setShowTagDropdown(true)}
                      />
                    </div>
                  </div>
                  
                  {showTagDropdown && (
                    <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto">
                      <div className="p-2 space-y-1">
                        {tags
                          .filter(tag => !selectedTags.includes(tag.id))
                          .map(tag => (
                            <div
                              key={tag.id}
                              className="flex items-center gap-2 px-2 py-1.5 hover:bg-gray-50 rounded-md cursor-pointer"
                              onClick={() => {
                                setSelectedTags([...selectedTags, tag.id]);
                                setShowTagDropdown(false);
                              }}
                            >
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: tag.color }}
                              />
                              <span className="text-sm text-gray-700">{tag.name}</span>
                            </div>
                          ))}
                        {tags.filter(tag => !selectedTags.includes(tag.id)).length === 0 && (
                          <div className="text-sm text-gray-500 text-center py-2">
                            Todas as tags foram selecionadas
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowModal(false)
                    setEditingTransaction(null)
                    setTransactionAmount(0)
                    reset()
                  }}
                  className="btn-secondary flex-1"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  className="btn-primary flex-1"
                >
                  {editingTransaction ? 'Atualizar' : 'Criar'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal de Comprovante */}
      <ReceiptModal
        isOpen={showReceiptModal}
        onClose={() => setShowReceiptModal(false)}
        receiptUrl={selectedReceipt?.url}
        transactionDescription={selectedReceipt?.description}
      />

      {/* Modal de Parcelamento */}
      <InstallmentModal
        isOpen={showInstallmentModal}
        onClose={() => setShowInstallmentModal(false)}
        transaction={selectedInstallment}
        installments={installmentDetails}
      />

      {/* Modal de Detalhes da Transação */}
      <TransactionDetailModal
        isOpen={showTransactionDetailModal}
        onClose={() => setShowTransactionDetailModal(false)}
        transaction={selectedTransactionDetail}
      />
    </div>
  )
}

export default Transactions
