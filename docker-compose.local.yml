version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=${NODE_ENV:-development}
        - REACT_APP_API_URL=${REACT_APP_API_URL:-http://backend:5000}
        - REACT_APP_TABLE_FONT_SIZE=${REACT_APP_TABLE_FONT_SIZE:-14px}
        - REACT_APP_TABLE_PADDING=${REACT_APP_TABLE_PADDING:-12px}
        - REACT_APP_CONTAINER_MAX_WIDTH=${REACT_APP_CONTAINER_MAX_WIDTH:-100%}
        - REACT_APP_TABLE_BG_COLOR=${REACT_APP_TABLE_BG_COLOR:-#f8f9fa}
    container_name: sara-frontend-local
    restart: unless-stopped
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    healthcheck:
      test: ["C<PERSON>", "wget", "--spider", "-q", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    depends_on:
      - backend
    networks:
      - sara-network
    volumes:
      - ./frontend:/app
      - /app/node_modules

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=${NODE_ENV:-development}
    container_name: sara-backend-local
    restart: unless-stopped
    ports:
      - "${BACKEND_PORT:-5000}:5000"
    environment:
      - DATABASE_URL=file:/app/prisma/dev.db
      - NODE_ENV=${NODE_ENV:-development}
      - JWT_SECRET=${JWT_SECRET:-your_jwt_secret_dev}
      - PORT=5000
      - CLOUDINARY_CLOUD_NAME=${CLOUDINARY_CLOUD_NAME}
      - CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY}
      - CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET}
    volumes:
      - backend_data_local:/app/prisma
      - ./backend:/app
      - /app/node_modules
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - sara-network

networks:
  sara-network:
    driver: bridge

volumes:
  backend_data_local:
