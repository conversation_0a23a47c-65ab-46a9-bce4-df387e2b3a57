import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'react-hot-toast'
import { FileText, FolderTree, X, ChevronDown, Palette } from 'lucide-react'
import { getIconCategories } from '../utils/getRandomIcon'
import { categoryService } from '../services/categoryService'

function CategoryModal({ isOpen, onClose, category, categories, onSuccess }) {
  const [loading, setLoading] = useState(false)
  const [showIconPicker, setShowIconPicker] = useState(false)
  const [selectedIconCategory, setSelectedIconCategory] = useState(null)
  const { register, handleSubmit, reset, setValue, watch, formState: { errors } } = useForm()
  const iconCategories = getIconCategories()

  useEffect(() => {
    if (isOpen) {
      if (category) {
        // Modo edição
        reset({
          name: category.name,
          color: category.color,
          icon: category.icon,
          parentId: category.parentId || ''
        })
      } else {
        // Modo criação - valores padrão
        reset({
          name: '',
          color: '#3B82F6',
          icon: '💰',
          parentId: ''
        })
      }
    } else {
      setShowIconPicker(false)
      setSelectedIconCategory(null)
    }
  }, [isOpen, category, reset])

  const handleColorChange = (color) => {
    setValue('color', color, { shouldValidate: true, shouldDirty: true })
  }

  const handleIconSelect = (icon) => {
    setValue('icon', icon, { shouldValidate: true, shouldDirty: true })
    setShowIconPicker(false)
  }

  const onSubmit = async (data) => {
    try {
      setLoading(true)

      if (category) {
        await categoryService.updateCategory(category.id, {
          name: data.name,
          color: data.color,
          icon: data.icon,
          parentId: data.parentId || null
        })
        toast.success('Categoria atualizada com sucesso!')
      } else {
        await categoryService.createCategory({
          name: data.name,
          color: data.color,
          icon: data.icon,
          parentId: data.parentId || null
        })
        toast.success('Categoria criada com sucesso!')
      }

      onSuccess()
      onClose()
    } catch (error) {
      console.error('Erro ao salvar categoria:', error)
      toast.error('Erro ao salvar categoria')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md transform transition-all">
        {/* Header */}
        <div 
          className="bg-gradient-to-r from-slate-700 to-slate-800 text-white p-6 rounded-t-2xl"
          style={{ backgroundColor: watch('color') }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-white/10 backdrop-blur rounded-xl flex items-center justify-center text-2xl shadow-lg">
                {watch('icon') || '💰'}
              </div>
              <div>
                <h2 className="text-2xl font-bold">
                  {category ? 'Editar Categoria' : 'Nova Categoria'}
                </h2>
                <p className="text-slate-300 text-sm mt-1">
                  {category ? 'Altere os dados da categoria' : 'Crie uma nova categoria'}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
          {/* Nome */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nome *
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                {...register('name', { required: 'Nome é obrigatório' })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent transition-all"
                placeholder="Ex: Alimentação"
              />
            </div>
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          {/* Categoria Pai */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Categoria Pai
            </label>
            <div className="relative">
              <FolderTree className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <select
                {...register('parentId')}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent appearance-none transition-all"
              >
                <option value="">Nenhuma (Categoria Principal)</option>
                {categories
                  .filter(c => !c.isSubcategory && (!category || c.id !== category.id))
                  .map((c) => (
                    <option key={c.id} value={c.id}>
                      {c.icon} {c.name}
                    </option>
                  ))}
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 pointer-events-none" />
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Opcional. Selecione uma categoria pai para criar uma sub-categoria.
            </p>
          </div>

          {/* Cor e Ícone */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Cor
              </label>
              <div className="relative">
                <input
                  type="color"
                  value={watch('color')}
                  onChange={(e) => handleColorChange(e.target.value)}
                  className="w-full h-12 rounded-xl cursor-pointer border border-gray-300 p-1"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ícone
              </label>
              <div className="relative">
                <button
                  type="button"
                  onClick={() => setShowIconPicker(!showIconPicker)}
                  className="w-full h-12 bg-gray-50 rounded-xl flex items-center justify-between px-4 hover:bg-gray-100 transition-colors border border-gray-300"
                >
                  <span className="text-2xl">{watch('icon')}</span>
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                </button>

                {/* Seletor de Ícones */}
                {showIconPicker && (
                  <div className="absolute z-10 mt-2 w-72 bg-white rounded-xl shadow-xl border border-gray-200 max-h-96 overflow-y-auto">
                    {iconCategories.map((cat) => (
                      <div key={cat.id} className="p-2">
                        <button
                          type="button"
                          onClick={() => setSelectedIconCategory(selectedIconCategory === cat.id ? null : cat.id)}
                          className="w-full text-left px-3 py-2 rounded-lg hover:bg-gray-100 flex items-center justify-between"
                        >
                          <span className="font-medium text-gray-700">{cat.name}</span>
                          <ChevronDown className={`h-4 w-4 text-gray-500 transform transition-transform ${
                            selectedIconCategory === cat.id ? 'rotate-180' : ''
                          }`} />
                        </button>
                        
                        {selectedIconCategory === cat.id && (
                          <div className="grid grid-cols-6 gap-1 p-2">
                            {cat.icons.map((icon, index) => (
                              <button
                                key={index}
                                type="button"
                                onClick={() => handleIconSelect(icon)}
                                className="aspect-square flex items-center justify-center text-xl hover:bg-slate-100 rounded-lg transition-colors"
                              >
                                {icon}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Botões */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-3 text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition-colors font-medium"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 px-4 py-3 bg-gradient-to-r from-slate-700 to-slate-800 text-white rounded-xl hover:from-slate-800 hover:to-slate-900 transition-all font-medium disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
            >
              {loading ? 'Salvando...' : category ? 'Salvar' : 'Criar'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default CategoryModal 