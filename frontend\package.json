{"name": "sara-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "recharts": "^2.8.0", "lucide-react": "^0.294.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "clsx": "^2.0.0", "react-beautiful-dnd": "^13.1.1", "react-grid-layout": "^1.5.1", "vite": "^4.5.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^4.5.0"}}