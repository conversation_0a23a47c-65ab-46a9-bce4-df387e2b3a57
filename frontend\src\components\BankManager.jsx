import React, { useState, useEffect } from 'react'
import {
  Plus,
  Edit3,
  Trash2,
  Eye,
  EyeOff,
  ArrowRightLeft,
  Wallet,
  DollarSign
} from 'lucide-react'
import { bankService } from '../services/bankService'
import CurrencyInput from './CurrencyInput'
import toast from 'react-hot-toast'

function BankManager() {
  const [banks, setBanks] = useState([])
  const [loading, setLoading] = useState(true)
  const [showBalances, setShowBalances] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showTransferModal, setShowTransferModal] = useState(false)
  const [editingBank, setEditingBank] = useState(null)
  const [totalBalance, setTotalBalance] = useState(0)

  const [newBank, setNewBank] = useState({
    name: '',
    icon: '🏦',
    color: '#3B82F6',
    initialBalance: 0
  })

  const bankIcons = ['🏦', '🐷', '💰', '💳', '🏧', '💎', '🎯', '📊']
  const bankColors = [
    '#3B82F6', '#22C55E', '#F59E0B', '#EF4444',
    '#8B5CF6', '#EC4899', '#14B8A6', '#6B7280'
  ]

  useEffect(() => {
    fetchBanks()
    fetchTotalBalance()
  }, [])

  const fetchBanks = async () => {
    try {
      const data = await bankService.getBanks()
      setBanks(data)
    } catch (error) {
      toast.error('Erro ao carregar bancos')
    } finally {
      setLoading(false)
    }
  }

  const fetchTotalBalance = async () => {
    try {
      const data = await bankService.getTotalBalance()
      setTotalBalance(data.totalBalance)
    } catch (error) {
      console.error('Erro ao carregar saldo total:', error)
    }
  }

  const handleCreateBank = async () => {
    try {
      if (!newBank.name) {
        toast.error('Nome do banco é obrigatório')
        return
      }

      await bankService.createBank(newBank)
      toast.success('Banco criado com sucesso!')
      setShowAddModal(false)
      setNewBank({ name: '', icon: '🏦', color: '#3B82F6', initialBalance: 0 })
      fetchBanks()
      fetchTotalBalance()
    } catch (error) {
      toast.error('Erro ao criar banco')
    }
  }

  const handleUpdateBank = async () => {
    try {
      await bankService.updateBank(editingBank.id, editingBank)
      toast.success('Banco atualizado com sucesso!')
      setShowEditModal(false)
      setEditingBank(null)
      fetchBanks()
    } catch (error) {
      toast.error('Erro ao atualizar banco')
    }
  }

  const handleDeleteBank = async (bank) => {
    if (!confirm(`Tem certeza que deseja excluir o banco "${bank.name}"?`)) {
      return
    }

    try {
      await bankService.deleteBank(bank.id)
      toast.success('Banco excluído com sucesso!')
      fetchBanks()
      fetchTotalBalance()
    } catch (error) {
      toast.error(error.response?.data?.error || 'Erro ao excluir banco')
    }
  }

  const toggleBankVisibility = async (bank) => {
    try {
      await bankService.updateBank(bank.id, { isVisible: !bank.isVisible })
      fetchBanks()
      fetchTotalBalance()
    } catch (error) {
      toast.error('Erro ao atualizar visibilidade')
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Bancos e Contas</h1>
          <p className="text-gray-600 mt-1">Gerencie suas contas e acompanhe seus saldos</p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowBalances(!showBalances)}
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {showBalances ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            <span>{showBalances ? 'Ocultar' : 'Mostrar'} Saldos</span>
          </button>
          <button
            onClick={() => setShowTransferModal(true)}
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-slate-600 rounded-lg hover:bg-slate-700 transition-colors"
          >
            <ArrowRightLeft className="h-4 w-4" />
            <span>Transferir</span>
          </button>
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-slate-900 rounded-lg hover:bg-slate-800 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Novo Banco</span>
          </button>
        </div>
      </div>

      {/* Saldo Total */}
      <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-8 text-white shadow-xl">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="h-5 w-5 text-slate-300" />
              <p className="text-slate-300 text-sm font-medium">Saldo Total Disponível</p>
            </div>
            <p className="text-4xl font-bold tracking-tight">
              {showBalances ? formatCurrency(totalBalance) : '••••••••'}
            </p>
            <p className="text-slate-400 text-sm mt-1">
              {banks.filter(b => b.isVisible).length} conta{banks.filter(b => b.isVisible).length !== 1 ? 's' : ''} ativa{banks.filter(b => b.isVisible).length !== 1 ? 's' : ''}
            </p>
          </div>
          <div className="bg-slate-700 bg-opacity-50 p-4 rounded-2xl">
            <Wallet className="h-10 w-10 text-slate-300" />
          </div>
        </div>
      </div>

      {/* Lista de Bancos */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {banks.map((bank) => (
          <div
            key={bank.id}
            className={`bg-white rounded-2xl border shadow-sm p-6 transition-all hover:shadow-md ${
              bank.isVisible
                ? 'border-gray-200 hover:border-gray-300'
                : 'border-gray-100 opacity-60 bg-gray-50'
            }`}
          >
            <div className="flex items-start justify-between mb-6">
              <div className="flex items-center gap-4">
                <div
                  className="w-14 h-14 rounded-2xl flex items-center justify-center text-2xl shadow-sm"
                  style={{ backgroundColor: bank.color + '15', color: bank.color, border: `2px solid ${bank.color}20` }}
                >
                  {bank.icon}
                </div>
                <div>
                  <h3 className="font-bold text-gray-900 text-lg">{bank.name}</h3>
                  <div className="flex items-center gap-2 mt-1">
                    <div className={`w-2 h-2 rounded-full ${bank.isVisible ? 'bg-green-500' : 'bg-gray-400'}`} />
                    <p className="text-sm text-gray-500">
                      {bank.isVisible ? 'Ativo' : 'Oculto'}
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <button
                  onClick={() => toggleBankVisibility(bank)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  title={bank.isVisible ? 'Ocultar' : 'Mostrar'}
                >
                  {bank.isVisible ? <Eye className="h-4 w-4 text-gray-600" /> : <EyeOff className="h-4 w-4 text-gray-400" />}
                </button>
                <button
                  onClick={() => {
                    setEditingBank(bank)
                    setShowEditModal(true)
                  }}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Editar"
                >
                  <Edit3 className="h-4 w-4 text-gray-600" />
                </button>
                <button
                  onClick={() => handleDeleteBank(bank)}
                  className="p-2 hover:bg-red-50 rounded-lg text-red-600 transition-colors"
                  title="Excluir"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-gray-50 rounded-xl p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-600">Saldo Atual</span>
                  <span className="text-xl font-bold text-gray-900">
                    {showBalances ? formatCurrency(bank.currentBalance) : '••••••••'}
                  </span>
                </div>
                {bank.initialBalance !== bank.currentBalance && (
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-500">Saldo Inicial</span>
                    <span className="text-gray-600 font-medium">
                      {showBalances ? formatCurrency(bank.initialBalance) : '••••••'}
                    </span>
                  </div>
                )}
              </div>

              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-500">Variação</span>
                <span className={`font-medium ${
                  bank.currentBalance >= bank.initialBalance ? 'text-green-600' : 'text-red-600'
                }`}>
                  {showBalances ? (
                    bank.currentBalance >= bank.initialBalance ? '+' : ''
                  ) + formatCurrency(bank.currentBalance - bank.initialBalance) : '••••••'}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {banks.length === 0 && (
        <div className="text-center py-12">
          <Wallet className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum banco cadastrado</h3>
          <p className="text-gray-600 mb-4">Comece criando sua primeira conta bancária</p>
          <button
            onClick={() => setShowAddModal(true)}
            className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Criar Primeiro Banco</span>
          </button>
        </div>
      )}

      {/* Modal Adicionar Banco */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Novo Banco</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                <input
                  type="text"
                  value={newBank.name}
                  onChange={(e) => setNewBank({ ...newBank, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: Conta Corrente"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ícone</label>
                <div className="flex flex-wrap gap-2">
                  {bankIcons.map((icon) => (
                    <button
                      key={icon}
                      onClick={() => setNewBank({ ...newBank, icon })}
                      className={`w-10 h-10 rounded-lg border-2 flex items-center justify-center text-lg transition-colors ${
                        newBank.icon === icon ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {icon}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Cor</label>
                <div className="flex flex-wrap gap-2">
                  {bankColors.map((color) => (
                    <button
                      key={color}
                      onClick={() => setNewBank({ ...newBank, color })}
                      className={`w-8 h-8 rounded-full border-2 transition-all ${
                        newBank.color === color ? 'border-gray-800 scale-110' : 'border-gray-300'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Saldo Inicial</label>
                <CurrencyInput
                  value={newBank.initialBalance}
                  onChange={(value) => setNewBank({ ...newBank, initialBalance: value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="R$ 0,00"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowAddModal(false)
                  setNewBank({ name: '', icon: '🏦', color: '#3B82F6', initialBalance: 0 })
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleCreateBank}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Criar Banco
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Editar Banco */}
      {showEditModal && editingBank && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Editar Banco</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                <input
                  type="text"
                  value={editingBank.name}
                  onChange={(e) => setEditingBank({ ...editingBank, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ícone</label>
                <div className="flex flex-wrap gap-2">
                  {bankIcons.map((icon) => (
                    <button
                      key={icon}
                      onClick={() => setEditingBank({ ...editingBank, icon })}
                      className={`w-10 h-10 rounded-lg border-2 flex items-center justify-center text-lg transition-colors ${
                        editingBank.icon === icon ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {icon}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Cor</label>
                <div className="flex flex-wrap gap-2">
                  {bankColors.map((color) => (
                    <button
                      key={color}
                      onClick={() => setEditingBank({ ...editingBank, color })}
                      className={`w-8 h-8 rounded-full border-2 transition-all ${
                        editingBank.color === color ? 'border-gray-800 scale-110' : 'border-gray-300'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowEditModal(false)
                  setEditingBank(null)
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleUpdateBank}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Salvar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default BankManager
