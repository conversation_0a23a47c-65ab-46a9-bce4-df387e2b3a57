@echo off
echo ========================================
echo    🐳 Sara - Docker Setup
echo    Iniciando com Docker Compose...
echo ========================================
echo.

set /p mode="Escolha o modo (1=Local, 2=Producao): "

if "%mode%"=="1" (
    set compose_file=docker-compose.local.yml
    set env_mode=development
    echo 🔧 Modo: Desenvolvimento Local
) else (
    set compose_file=docker-compose.yml
    set env_mode=production
    echo 🚀 Modo: Producao (GitHub)
)

echo.

echo ⏳ Verificando Docker...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker nao encontrado!
    echo Instale o Docker Desktop primeiro
    pause
    exit /b 1
)

echo ⏳ Verificando Docker Compose...
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose nao encontrado!
    echo Instale o Docker Compose primeiro
    pause
    exit /b 1
)

echo ⏳ Verificando arquivo .env...
if not exist .env (
    echo ⚠️  Arquivo .env nao encontrado, copiando .env.example...
    copy .env.example .env
    echo.
    echo ❗ IMPORTANTE: Configure o arquivo .env antes de continuar!
    echo    Especialmente:
    if "%mode%"=="2" (
        echo    - GITHUB_TOKEN (obrigatorio para producao)
        echo    - CLOUDINARY_* (para upload de imagens)
    ) else (
        echo    - CLOUDINARY_* (para upload de imagens)
    )
    echo.
    pause
)

echo ✅ Parando containers existentes...
docker-compose -f %compose_file% down

echo.
echo ✅ Iniciando containers (%env_mode%)...
echo.
echo 🌐 O sistema sera aberto em:
echo    Frontend: http://localhost:3000
echo    Backend:  http://localhost:5000
echo.
if "%mode%"=="2" (
    echo 🔄 Auto-deploy: Ativo (Watchtower monitorando)
    echo 📡 Webhook: Configure em http://localhost:9000
    echo.
)
echo 👤 Credenciais de teste:
echo    Email: <EMAIL>
echo    Senha: 123456
echo.
echo ⚠️  Para parar o sistema, pressione Ctrl+C
echo.

docker-compose -f %compose_file% up --build

echo.
echo 🛑 Sistema parado.
pause
