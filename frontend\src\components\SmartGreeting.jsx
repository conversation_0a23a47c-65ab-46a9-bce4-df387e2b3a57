import React, { useState, useEffect } from 'react'
import { 
  Coffee, 
  Sun, 
  Moon, 
  ShoppingCart, 
  CreditCard, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Target,
  Sparkles
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'

function SmartGreeting({ stats, recentTransactions = [] }) {
  const { user } = useAuth()
  const [greeting, setGreeting] = useState('')
  const [message, setMessage] = useState('')
  const [icon, setIcon] = useState(null)
  const [bgColor, setBgColor] = useState('bg-gradient-to-r from-blue-500 to-indigo-600')

  useEffect(() => {
    generateSmartMessage()
  }, [user, stats, recentTransactions])

  const generateSmartMessage = () => {
    const hour = new Date().getHours()
    const firstName = user?.name?.split(' ')[0] || 'Usuário'
    
    // Definir saudação baseada no horário
    let timeGreeting = ''
    let timeIcon = null
    
    if (hour >= 5 && hour < 12) {
      timeGreeting = 'Bom dia'
      timeIcon = <Sun className="h-5 w-5" />
    } else if (hour >= 12 && hour < 18) {
      timeGreeting = 'Boa tarde'
      timeIcon = <Sun className="h-5 w-5" />
    } else {
      timeGreeting = 'Boa noite'
      timeIcon = <Moon className="h-5 w-5" />
    }

    setGreeting(`${timeGreeting}, ${firstName}!`)
    setIcon(timeIcon)

    // Gerar mensagem inteligente baseada nos dados
    const messages = generateIntelligentMessages()
    const selectedMessage = messages[Math.floor(Math.random() * messages.length)]
    
    setMessage(selectedMessage.text)
    setBgColor(selectedMessage.color)
  }

  const generateIntelligentMessages = () => {
    const messages = []
    const today = new Date()
    const dayOfWeek = today.getDay()
    const dayOfMonth = today.getDate()
    
    // Mensagens baseadas no dia da semana
    if (dayOfWeek === 1) { // Segunda-feira
      messages.push({
        text: "Como foi o fim de semana? Gastou algo especial? 🛍️",
        color: "bg-gradient-to-r from-purple-500 to-pink-600"
      })
    } else if (dayOfWeek === 5) { // Sexta-feira
      messages.push({
        text: "Sexta-feira! Que tal uma pizza? Lembre-se de registrar os gastos! 🍕",
        color: "bg-gradient-to-r from-orange-500 to-red-600"
      })
    } else if (dayOfWeek === 6 || dayOfWeek === 0) { // Fim de semana
      messages.push({
        text: "Fim de semana é hora de relaxar! Mas não esqueça do orçamento 😉",
        color: "bg-gradient-to-r from-green-500 to-teal-600"
      })
    }

    // Mensagens baseadas no dia do mês
    if (dayOfMonth <= 5) {
      messages.push({
        text: "Início do mês! Hora de planejar os gastos e definir metas 🎯",
        color: "bg-gradient-to-r from-blue-500 to-indigo-600"
      })
    } else if (dayOfMonth >= 25) {
      messages.push({
        text: "Final do mês chegando! Como está o orçamento? 📊",
        color: "bg-gradient-to-r from-yellow-500 to-orange-600"
      })
    }

    // Mensagens baseadas nas estatísticas
    if (stats) {
      if (stats.totalExpenses > stats.totalIncome) {
        messages.push({
          text: "Atenção! Seus gastos estão maiores que a renda. Que tal revisar? ⚠️",
          color: "bg-gradient-to-r from-red-500 to-pink-600"
        })
      } else if (stats.totalIncome > stats.totalExpenses * 1.5) {
        messages.push({
          text: "Parabéns! Você está economizando bem este mês! 🎉",
          color: "bg-gradient-to-r from-green-500 to-emerald-600"
        })
      }

      if (stats.totalExpenses === 0) {
        messages.push({
          text: "Ainda não registrou gastos hoje? Que tal começar? 📝",
          color: "bg-gradient-to-r from-indigo-500 to-purple-600"
        })
      }
    }

    // Mensagens baseadas nas transações recentes
    if (recentTransactions.length > 0) {
      const lastTransaction = recentTransactions[0]
      const lastTransactionDate = new Date(lastTransaction.date)
      const daysSinceLastTransaction = Math.floor((today - lastTransactionDate) / (1000 * 60 * 60 * 24))
      
      if (daysSinceLastTransaction > 3) {
        messages.push({
          text: "Faz tempo que não vejo movimentações! Comprou algo legal? 🛒",
          color: "bg-gradient-to-r from-cyan-500 to-blue-600"
        })
      } else if (lastTransaction.type === 'EXPENSE' && lastTransaction.amount > 100) {
        messages.push({
          text: "Vi que fez uma compra grande recentemente! Foi um bom investimento? 💰",
          color: "bg-gradient-to-r from-amber-500 to-orange-600"
        })
      }
    }

    // Mensagens motivacionais gerais
    messages.push(
      {
        text: "Como você está? Vamos organizar as finanças juntos! 💪",
        color: "bg-gradient-to-r from-blue-500 to-indigo-600"
      },
      {
        text: "Que tal definir uma meta de economia para este mês? 🎯",
        color: "bg-gradient-to-r from-green-500 to-teal-600"
      },
      {
        text: "Lembre-se: pequenas economias fazem grande diferença! ✨",
        color: "bg-gradient-to-r from-purple-500 to-pink-600"
      },
      {
        text: "Está controlando bem seus gastos! Continue assim! 🚀",
        color: "bg-gradient-to-r from-emerald-500 to-green-600"
      }
    )

    return messages
  }

  return (
    <div className={`${bgColor} rounded-2xl p-6 text-white shadow-lg`}>
      <div className="flex items-center gap-3 mb-3">
        {icon}
        <h2 className="text-2xl font-bold">{greeting}</h2>
      </div>
      <p className="text-lg text-white text-opacity-90 leading-relaxed">
        {message}
      </p>
      
      {/* Estatísticas rápidas */}
      {stats && (
        <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white bg-opacity-20 rounded-lg p-3 text-center">
            <div className="text-sm text-white text-opacity-80">Receitas</div>
            <div className="text-lg font-bold">
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL',
                minimumFractionDigits: 0
              }).format(stats.totalIncome || 0)}
            </div>
          </div>
          
          <div className="bg-white bg-opacity-20 rounded-lg p-3 text-center">
            <div className="text-sm text-white text-opacity-80">Despesas</div>
            <div className="text-lg font-bold">
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL',
                minimumFractionDigits: 0
              }).format(stats.totalExpenses || 0)}
            </div>
          </div>
          
          <div className="bg-white bg-opacity-20 rounded-lg p-3 text-center">
            <div className="text-sm text-white text-opacity-80">Saldo</div>
            <div className={`text-lg font-bold ${
              (stats.totalIncome - stats.totalExpenses) >= 0 ? 'text-green-200' : 'text-red-200'
            }`}>
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL',
                minimumFractionDigits: 0
              }).format((stats.totalIncome || 0) - (stats.totalExpenses || 0))}
            </div>
          </div>
          
          <div className="bg-white bg-opacity-20 rounded-lg p-3 text-center">
            <div className="text-sm text-white text-opacity-80">Transações</div>
            <div className="text-lg font-bold">
              {stats.totalTransactions || 0}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default SmartGreeting
