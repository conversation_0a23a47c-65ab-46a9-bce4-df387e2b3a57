import React, { createContext, useContext, useState, useEffect } from 'react'
import api from '../services/api'

const AuthContext = createContext()

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider')
  }
  return context
}

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const initAuth = async () => {
      try {
        const token = localStorage.getItem('token')
        if (token) {
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`
          await checkAuth()
        } else {
          // Sem token, permitir acesso como visitante
          setLoading(false)
        }
      } catch (error) {
        console.error('Erro na inicialização da autenticação:', error)
        setLoading(false)
      }
    }

    // Timeout de segurança para evitar loading infinito
    const safetyTimeout = setTimeout(() => {
      console.warn('Timeout na inicialização da autenticação')
      setLoading(false)
    }, 10000)

    initAuth().finally(() => {
      clearTimeout(safetyTimeout)
    })
  }, [])

  const checkAuth = async () => {
    try {
      // Timeout de 5 segundos para evitar travamento
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout')), 5000)
      )

      const apiPromise = api.get('/auth/me')

      const response = await Promise.race([apiPromise, timeoutPromise])
      setUser(response.data.user)
    } catch (error) {
      console.error('Erro na verificação de autenticação:', error)
      localStorage.removeItem('token')
      delete api.defaults.headers.common['Authorization']

      // Se for erro de conexão, permitir acesso offline
      if (error.message === 'Timeout' || error.code === 'ECONNREFUSED') {
        console.warn('Backend indisponível, permitindo acesso offline')
        setUser({ id: 'offline', name: 'Usuário Offline', email: 'offline@local' })
      }
    } finally {
      setLoading(false)
    }
  }

  const login = async (email, password) => {
    try {
      const response = await api.post('/auth/login', { email, password })
      const { user, token } = response.data

      localStorage.setItem('token', token)
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`
      setUser(user)

      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.error || 'Erro no login'
      }
    }
  }

  const register = async (name, email, password) => {
    try {
      const response = await api.post('/auth/register', { name, email, password })
      const { user, token } = response.data

      localStorage.setItem('token', token)
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`
      setUser(user)

      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.error || 'Erro no registro'
      }
    }
  }

  const logout = () => {
    localStorage.removeItem('token')
    delete api.defaults.headers.common['Authorization']
    setUser(null)
  }

  const updateProfile = async (profileData) => {
    try {
      const formData = new FormData()

      // Adicionar campos de texto
      Object.keys(profileData).forEach(key => {
        if (key !== 'photo' && profileData[key]) {
          formData.append(key, profileData[key])
        }
      })

      // Adicionar foto se existir
      if (profileData.photo) {
        formData.append('photo', profileData.photo)
      }

      const response = await api.put('/auth/profile', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      setUser(response.data.user)
      return response.data
    } catch (error) {
      throw error
    }
  }

  const value = {
    user,
    loading,
    login,
    register,
    logout,
    updateProfile
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
