// Mapeamento de ícones para bancos conhecidos
export const bankIconMapping = {
  // Bancos Digitais
  'nubank': '💜',
  'inter': '🧡', 
  'c6 bank': '⚫',
  'c6': '⚫',
  'original': '🟢',
  'next': '🟣',
  'neon': '🔵',
  'digio': '🔴',
  'will bank': '🟡',
  'will': '🟡',
  'mercado pago': '💙',
  'mercadopago': '💙',
  'picpay': '🟢',
  'pagseguro': '🟡',
  'stone': '🟢',
  'conta simples': '🔵',
  'contasimples': '🔵',
  
  // Bancos Tradicionais
  'banco do brasil': '🟡',
  'bb': '🟡',
  'caixa': '🔵',
  'caixa econômica': '🔵',
  'cef': '🔵',
  'itaú': '🧡',
  'itau': '🧡',
  'bradesco': '🔴',
  'santander': '🔴',
  'hsbc': '🔴',
  'citibank': '🔵',
  'citi': '🔵',
  'safra': '🔵',
  'votorantim': '🟣',
  'banrisul': '🔵',
  'sicredi': '🟢',
  'sicoob': '🟢',
  'unicred': '🟢',
  'btg pactual': '⚫',
  'btg': '⚫',
  'xp investimentos': '🟡',
  'xp': '🟡',
  'modal': '🔵',
  'rico': '🟢',
  'clear': '🔵',
  'easynvest': '🟢',
  'avenue': '🔵',
  'toro': '🔴',
  
  // Carteiras Digitais
  'paypal': '🔵',
  'google pay': '🟢',
  'apple pay': '⚫',
  'samsung pay': '🔵',
  
  // Criptomoedas
  'binance': '🟡',
  'coinbase': '🔵',
  'mercado bitcoin': '🧡',
  'foxbit': '🧡',
  'bitso': '🟢',
  'novadax': '🔵',
  
  // Genéricos
  'carteira': '💰',
  'dinheiro': '💵',
  'poupança': '🐷',
  'poupanca': '🐷',
  'conta corrente': '🏦',
  'investimento': '📈',
  'investimentos': '📈'
}

// Função para obter ícone baseado no nome do banco
export const getBankIcon = (bankName) => {
  if (!bankName) return '🏦'
  
  const normalizedName = bankName.toLowerCase().trim()
  
  // Busca exata primeiro
  if (bankIconMapping[normalizedName]) {
    return bankIconMapping[normalizedName]
  }
  
  // Busca por palavras-chave
  for (const [key, icon] of Object.entries(bankIconMapping)) {
    if (normalizedName.includes(key) || key.includes(normalizedName)) {
      return icon
    }
  }
  
  // Ícone padrão
  return '🏦'
}

// Lista de ícones disponíveis para seleção manual
export const availableBankIcons = [
  '🏦', '💜', '🧡', '⚫', '🟢', '🟣', '🔵', '🔴', '🟡', '💙',
  '💰', '💵', '🐷', '📈', '💳', '💎', '🎯', '📊', '⭐', '🔥',
  '💸', '🪙', '💷', '💶', '💴', '💹', '📉', '🏧', '💻', '📱'
]

// Função para obter cor baseada no banco
export const getBankColor = (bankName) => {
  if (!bankName) return '#3B82F6'
  
  const normalizedName = bankName.toLowerCase().trim()
  
  const colorMapping = {
    'nubank': '#8A2BE2',
    'inter': '#FF8C00', 
    'c6 bank': '#000000',
    'c6': '#000000',
    'original': '#00FF00',
    'next': '#8A2BE2',
    'neon': '#0000FF',
    'digio': '#FF0000',
    'will bank': '#FFD700',
    'will': '#FFD700',
    'mercado pago': '#00BFFF',
    'mercadopago': '#00BFFF',
    'picpay': '#00FF00',
    'pagseguro': '#FFD700',
    'stone': '#00FF00',
    'conta simples': '#0000FF',
    'contasimples': '#0000FF',
    'banco do brasil': '#FFD700',
    'bb': '#FFD700',
    'caixa': '#0000FF',
    'caixa econômica': '#0000FF',
    'cef': '#0000FF',
    'itaú': '#FF8C00',
    'itau': '#FF8C00',
    'bradesco': '#FF0000',
    'santander': '#FF0000',
    'hsbc': '#FF0000',
    'citibank': '#0000FF',
    'citi': '#0000FF',
    'safra': '#0000FF',
    'votorantim': '#8A2BE2',
    'banrisul': '#0000FF',
    'sicredi': '#00FF00',
    'sicoob': '#00FF00',
    'unicred': '#00FF00',
    'btg pactual': '#000000',
    'btg': '#000000',
    'xp investimentos': '#FFD700',
    'xp': '#FFD700',
    'modal': '#0000FF',
    'rico': '#00FF00',
    'clear': '#0000FF',
    'easynvest': '#00FF00',
    'avenue': '#0000FF',
    'toro': '#FF0000'
  }
  
  // Busca exata primeiro
  if (colorMapping[normalizedName]) {
    return colorMapping[normalizedName]
  }
  
  // Busca por palavras-chave
  for (const [key, color] of Object.entries(colorMapping)) {
    if (normalizedName.includes(key) || key.includes(normalizedName)) {
      return color
    }
  }
  
  // Cor padrão
  return '#3B82F6'
}
