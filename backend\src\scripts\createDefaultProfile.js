const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createDefaultProfiles() {
  try {
    // Buscar todos os usuários que não têm perfil padrão
    const users = await prisma.user.findMany({
      include: {
        dashboardProfiles: true
      }
    });

    for (const user of users) {
      if (user.dashboardProfiles.length === 0) {
        // Criar perfil padrão para o usuário
        await prisma.dashboardProfile.create({
          data: {
            userId: user.id,
            name: 'Dashboard Principal',
            description: 'Perfil padrão do dashboard',
            isDefault: true
          }
        });
        
        console.log(`Perfil padrão criado para usuário: ${user.email}`);
      }
    }

    console.log('Perfis padrão criados com sucesso!');
  } catch (error) {
    console.error('Erro ao criar perfis padrão:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createDefaultProfiles();
