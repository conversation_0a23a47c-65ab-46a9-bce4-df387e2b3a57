# 🐳 Docker Setup - Sistema Sara

Este guia explica como executar o Sistema Sara usando Docker e Docker Compose.

## 📋 Pré-requisitos

- Docker
- Docker Compose
- Git

## 🚀 Execução Rápida

1. **Clone o repositório:**
```bash
git clone <url-do-repositorio>
cd sara
```

2. **Configure as variáveis de ambiente (opcional):**
```bash
cp .env.example .env
# Edite o arquivo .env conforme necessário
```

3. **Execute com Docker Compose:**
```bash
docker-compose up --build
```

4. **Acesse a aplicação:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000

## ⚙️ Configuração

### Variáveis de Ambiente

Crie um arquivo `.env` baseado no `.env.example`:

```env
# Node Environment
NODE_ENV=production

# Frontend Configuration
FRONTEND_PORT=3000
REACT_APP_API_URL=http://backend:5000

# Backend Configuration
BACKEND_PORT=5000
JWT_SECRET=your_jwt_secret_change_this_in_production
```

### Portas Personalizadas

Para usar portas diferentes:

```env
FRONTEND_PORT=8080
BACKEND_PORT=8081
REACT_APP_API_URL=http://backend:8081
```

## 🔧 Comandos Úteis

### Executar em background:
```bash
docker-compose up -d
```

### Ver logs:
```bash
docker-compose logs -f
```

### Parar os containers:
```bash
docker-compose down
```

### Rebuild completo:
```bash
docker-compose down
docker-compose up --build --force-recreate
```

### Limpar volumes (⚠️ apaga dados):
```bash
docker-compose down -v
```

## 🗄️ Banco de Dados

O banco de dados SQLite é persistido em um volume Docker. Os dados são mantidos entre reinicializações dos containers.

Para resetar o banco de dados:
```bash
docker-compose down -v
docker-compose up --build
```

## 🔍 Troubleshooting

### Frontend não consegue se comunicar com Backend

1. Verifique se `REACT_APP_API_URL` está configurado corretamente
2. Verifique se ambos os containers estão na mesma rede
3. Verifique os logs: `docker-compose logs backend`

### Erro de porta em uso

1. Pare outros serviços usando as portas 3000 ou 5000
2. Ou configure portas diferentes no `.env`

### Erro de build

1. Limpe o cache do Docker:
```bash
docker system prune -a
```

2. Rebuild completo:
```bash
docker-compose up --build --force-recreate
```

## 📊 Health Checks

Os containers incluem health checks automáticos:

- Frontend: `http://localhost:3000/health`
- Backend: `http://localhost:5000/health`

## 🔄 Watchtower (Auto-update)

O setup inclui Watchtower para atualizações automáticas (desabilitado por padrão).

Para habilitar, descomente as configurações no `docker-compose.yml`.

## 🛡️ Segurança

Para produção:

1. Altere `JWT_SECRET` para um valor seguro
2. Configure HTTPS
3. Use um banco de dados externo (PostgreSQL/MySQL)
4. Configure backup dos volumes
