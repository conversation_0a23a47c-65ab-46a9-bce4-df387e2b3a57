const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Obter layout do usuário
router.get('/:page', async (req, res) => {
  try {
    const userId = req.user.id;
    const { page } = req.params;

    const layout = await prisma.dashboardLayout.findUnique({
      where: {
        userId_page: {
          userId,
          page
        }
      }
    });

    if (layout) {
      res.json({ layout: JSON.parse(layout.layout) });
    } else {
      // Layout padrão
      const defaultLayout = getDefaultLayout(page);
      res.json({ layout: defaultLayout });
    }
  } catch (error) {
    console.error('Erro ao buscar layout:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Salvar layout do usuário
router.post('/:page', async (req, res) => {
  try {
    const userId = req.user.id;
    const { page } = req.params;
    const { layout } = req.body;

    await prisma.dashboardLayout.upsert({
      where: {
        userId_page: {
          userId,
          page
        }
      },
      update: {
        layout: JSON.stringify(layout)
      },
      create: {
        userId,
        page,
        layout: JSON.stringify(layout)
      }
    });

    res.json({ message: 'Layout salvo com sucesso' });
  } catch (error) {
    console.error('Erro ao salvar layout:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

function getDefaultLayout(page) {
  if (page === 'analytics') {
    return {
      charts: [
        {
          id: 'expenses-by-category',
          type: 'pie',
          title: 'Gastos por Categoria',
          position: { x: 0, y: 0, w: 6, h: 4 },
          visible: true,
          color: '#3B82F6',
          size: 'medium'
        },
        {
          id: 'weekly-expenses',
          type: 'bar',
          title: 'Gastos por Dia da Semana',
          position: { x: 6, y: 0, w: 6, h: 4 },
          visible: true,
          color: '#EF4444',
          size: 'medium'
        },
        {
          id: 'daily-flow',
          type: 'line',
          title: 'Fluxo Diário',
          position: { x: 0, y: 4, w: 12, h: 4 },
          visible: true,
          color: '#10B981',
          size: 'large'
        }
      ]
    };
  }
  
  return {
    charts: [
      {
        id: 'balance',
        type: 'area',
        title: 'Saldo Mensal',
        position: { x: 0, y: 0, w: 6, h: 4 },
        visible: true,
        color: '#3B82F6',
        size: 'medium'
      },
      {
        id: 'revenue',
        type: 'bar',
        title: 'Receitas vs Despesas',
        position: { x: 6, y: 0, w: 6, h: 4 },
        visible: true,
        color: '#10B981',
        size: 'medium'
      }
    ]
  };
}

module.exports = router;
