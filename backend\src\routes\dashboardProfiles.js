const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Obter todos os perfis do usuário
router.get('/', async (req, res) => {
  try {
    const userId = req.user.id;

    let profiles = await prisma.dashboardProfile.findMany({
      where: { userId },
      include: {
        cards: true,
        _count: {
          select: { cards: true }
        }
      },
      orderBy: [
        { isDefault: 'desc' },
        { createdAt: 'asc' }
      ]
    });

    // Se não há perfis, criar um perfil padrão
    if (profiles.length === 0) {
      console.log('Nenhum perfil encontrado, criando perfil padrão...');

      const defaultProfile = await prisma.dashboardProfile.create({
        data: {
          userId,
          name: 'Dashboard Principal',
          description: 'Perfil padrão do dashboard',
          isDefault: true
        },
        include: {
          cards: true,
          _count: {
            select: { cards: true }
          }
        }
      });

      profiles = [defaultProfile];
      console.log('Perfil padrão criado com sucesso');
    }

    res.json(profiles);
  } catch (error) {
    console.error('Erro ao buscar perfis:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo perfil
router.post('/', async (req, res) => {
  try {
    const userId = req.user.id;
    const { name, description, isDefault } = req.body;

    // Se este perfil for marcado como padrão, desmarcar outros
    if (isDefault) {
      await prisma.dashboardProfile.updateMany({
        where: { userId, isDefault: true },
        data: { isDefault: false }
      });
    }

    const profile = await prisma.dashboardProfile.create({
      data: {
        userId,
        name,
        description,
        isDefault: isDefault || false
      },
      include: {
        _count: {
          select: { cards: true }
        }
      }
    });

    res.json(profile);
  } catch (error) {
    console.error('Erro ao criar perfil:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar perfil
router.put('/:id', async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const { name, description, isDefault } = req.body;

    // Se este perfil for marcado como padrão, desmarcar outros
    if (isDefault) {
      await prisma.dashboardProfile.updateMany({
        where: { userId, isDefault: true, id: { not: id } },
        data: { isDefault: false }
      });
    }

    const profile = await prisma.dashboardProfile.update({
      where: { id, userId },
      data: {
        name,
        description,
        isDefault
      },
      include: {
        _count: {
          select: { cards: true }
        }
      }
    });

    res.json(profile);
  } catch (error) {
    console.error('Erro ao atualizar perfil:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar perfil
router.delete('/:id', async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    // Verificar se não é o último perfil
    const profileCount = await prisma.dashboardProfile.count({
      where: { userId }
    });

    if (profileCount <= 1) {
      return res.status(400).json({ error: 'Não é possível deletar o último perfil' });
    }

    // Verificar se é o perfil padrão
    const profile = await prisma.dashboardProfile.findFirst({
      where: { id, userId }
    });

    if (profile?.isDefault) {
      // Definir outro perfil como padrão
      await prisma.dashboardProfile.updateMany({
        where: { userId, id: { not: id } },
        data: { isDefault: true },
        take: 1
      });
    }

    await prisma.dashboardProfile.delete({
      where: { id, userId }
    });

    res.json({ message: 'Perfil removido com sucesso' });
  } catch (error) {
    console.error('Erro ao remover perfil:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Definir perfil como padrão
router.put('/:id/set-default', async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    // Desmarcar todos os outros perfis como padrão
    await prisma.dashboardProfile.updateMany({
      where: { userId, isDefault: true },
      data: { isDefault: false }
    });

    // Marcar este perfil como padrão
    const profile = await prisma.dashboardProfile.update({
      where: { id, userId },
      data: { isDefault: true },
      include: {
        _count: {
          select: { cards: true }
        }
      }
    });

    res.json(profile);
  } catch (error) {
    console.error('Erro ao definir perfil padrão:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Duplicar perfil
router.post('/:id/duplicate', async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const { name } = req.body;

    // Buscar perfil original com cards
    const originalProfile = await prisma.dashboardProfile.findFirst({
      where: { id, userId },
      include: { cards: true }
    });

    if (!originalProfile) {
      return res.status(404).json({ error: 'Perfil não encontrado' });
    }

    // Criar novo perfil
    const newProfile = await prisma.dashboardProfile.create({
      data: {
        userId,
        name: name || `${originalProfile.name} (Cópia)`,
        description: originalProfile.description,
        isDefault: false
      }
    });

    // Duplicar cards
    const cardPromises = originalProfile.cards.map(card =>
      prisma.dashboardCard.create({
        data: {
          userId,
          profileId: newProfile.id,
          title: card.title,
          type: card.type,
          position: card.position,
          config: card.config,
          categories: card.categories,
          visible: card.visible
        }
      })
    );

    await Promise.all(cardPromises);

    // Buscar perfil completo
    const completeProfile = await prisma.dashboardProfile.findFirst({
      where: { id: newProfile.id },
      include: {
        _count: {
          select: { cards: true }
        }
      }
    });

    res.json(completeProfile);
  } catch (error) {
    console.error('Erro ao duplicar perfil:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
