const express = require('express')
const router = express.Router()
const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

// Reset de transações
router.delete('/transactions', async (req, res) => {
  try {
    await prisma.transaction.deleteMany({})
    console.log('✅ Todas as transações foram removidas')
    res.json({ message: 'Transações removidas com sucesso' })
  } catch (error) {
    console.error('❌ Erro ao remover transações:', error)
    res.status(500).json({ error: 'Erro ao remover transações' })
  }
})

// Reset de bancos e métodos de pagamento
router.delete('/banks', async (req, res) => {
  try {
    await prisma.saving.deleteMany({})
    await prisma.paymentMethod.deleteMany({})
    await prisma.bank.deleteMany({})
    console.log('✅ Todos os bancos e métodos de pagamento foram removidos')
    res.json({ message: 'Bancos removidos com sucesso' })
  } catch (error) {
    console.error('❌ Erro ao remover bancos:', error)
    res.status(500).json({ error: 'Erro ao remover bancos' })
  }
})

// Reset de categorias
router.delete('/categories', async (req, res) => {
  try {
    await prisma.category.deleteMany({})
    console.log('✅ Todas as categorias foram removidas')
    res.json({ message: 'Categorias removidas com sucesso' })
  } catch (error) {
    console.error('❌ Erro ao remover categorias:', error)
    res.status(500).json({ error: 'Erro ao remover categorias' })
  }
})

// Reset de empréstimos
router.delete('/loans', async (req, res) => {
  try {
    await prisma.loanPayment.deleteMany({})
    await prisma.loan.deleteMany({})
    await prisma.contact.deleteMany({})
    console.log('✅ Todos os empréstimos foram removidos')
    res.json({ message: 'Empréstimos removidos com sucesso' })
  } catch (error) {
    console.error('❌ Erro ao remover empréstimos:', error)
    res.status(500).json({ error: 'Erro ao remover empréstimos' })
  }
})

// Reset de assinaturas
router.delete('/subscriptions', async (req, res) => {
  try {
    await prisma.subscription.deleteMany({})
    console.log('✅ Todas as assinaturas foram removidas')
    res.json({ message: 'Assinaturas removidas com sucesso' })
  } catch (error) {
    console.error('❌ Erro ao remover assinaturas:', error)
    res.status(500).json({ error: 'Erro ao remover assinaturas' })
  }
})

// Reset de cofrinhos
router.delete('/savings', async (req, res) => {
  try {
    await prisma.saving.deleteMany({})
    console.log('✅ Todos os cofrinhos foram removidos')
    res.json({ message: 'Cofrinhos removidos com sucesso' })
  } catch (error) {
    console.error('❌ Erro ao remover cofrinhos:', error)
    res.status(500).json({ error: 'Erro ao remover cofrinhos' })
  }
})

// Reset de configurações de dashboard
router.delete('/dashboard-cards', async (req, res) => {
  try {
    // Se houver tabela de configurações de dashboard, limpar aqui
    // await db.run('DELETE FROM dashboard_cards')
    console.log('✅ Configurações de dashboard foram removidas')
    res.json({ message: 'Configurações de dashboard removidas com sucesso' })
  } catch (error) {
    console.error('❌ Erro ao remover configurações de dashboard:', error)
    res.status(500).json({ error: 'Erro ao remover configurações de dashboard' })
  }
})

// Verificar usuário de teste
router.get('/verify-test-user', async (req, res) => {
  try {
    const user = await prisma.user.findUnique({
      where: { username: 'admin' }
    })

    if (user) {
      console.log('✅ Usuário de teste encontrado')
      res.json({ message: 'Usuário de teste existe', user: { username: user.username } })
    } else {
      console.log('⚠️ Usuário de teste não encontrado, criando...')
      // Criar usuário de teste se não existir
      const hashedPassword = await bcrypt.hash('admin', 10)

      const newUser = await prisma.user.create({
        data: {
          username: 'admin',
          email: '<EMAIL>',
          password: hashedPassword
        }
      })

      console.log('✅ Usuário de teste criado')
      res.json({ message: 'Usuário de teste criado', user: { username: newUser.username } })
    }
  } catch (error) {
    console.error('❌ Erro ao verificar usuário de teste:', error)
    res.status(500).json({ error: 'Erro ao verificar usuário de teste' })
  }
})

// Criar categorias padrão
router.post('/create-default-categories', async (req, res) => {
  try {
    // Primeiro, encontrar o usuário admin
    const adminUser = await prisma.user.findUnique({
      where: { username: 'admin' }
    })

    if (!adminUser) {
      return res.status(404).json({ error: 'Usuário admin não encontrado' })
    }

    const defaultCategories = [
      { name: 'Alimentação', icon: '🍽️', type: 'EXPENSE' },
      { name: 'Transporte', icon: '🚗', type: 'EXPENSE' },
      { name: 'Moradia', icon: '🏠', type: 'EXPENSE' },
      { name: 'Saúde', icon: '🏥', type: 'EXPENSE' },
      { name: 'Educação', icon: '📚', type: 'EXPENSE' },
      { name: 'Lazer', icon: '🎮', type: 'EXPENSE' },
      { name: 'Compras', icon: '🛒', type: 'EXPENSE' },
      { name: 'Serviços', icon: '🔧', type: 'EXPENSE' },
      { name: 'Salário', icon: '💰', type: 'INCOME' },
      { name: 'Freelance', icon: '💼', type: 'INCOME' },
      { name: 'Investimentos', icon: '📈', type: 'INCOME' },
      { name: 'Outros', icon: '📦', type: 'EXPENSE' }
    ]

    for (const category of defaultCategories) {
      await prisma.category.create({
        data: {
          name: category.name,
          icon: category.icon,
          type: category.type,
          userId: adminUser.id
        }
      })
    }

    console.log('✅ Categorias padrão criadas')
    res.json({ message: 'Categorias padrão criadas com sucesso', count: defaultCategories.length })
  } catch (error) {
    console.error('❌ Erro ao criar categorias padrão:', error)
    res.status(500).json({ error: 'Erro ao criar categorias padrão' })
  }
})

// Reset completo (todas as tabelas de uma vez)
router.delete('/all', async (req, res) => {
  try {
    console.log('🚀 Iniciando reset completo do banco de dados...')

    // Ordem importante devido às foreign keys
    await prisma.loanPayment.deleteMany({})
    await prisma.loan.deleteMany({})
    await prisma.contact.deleteMany({})
    await prisma.transaction.deleteMany({})
    await prisma.subscription.deleteMany({})
    await prisma.saving.deleteMany({})
    await prisma.paymentMethod.deleteMany({})
    await prisma.bank.deleteMany({})
    await prisma.category.deleteMany({})

    console.log('✅ Reset completo finalizado')
    res.json({ message: 'Reset completo realizado com sucesso' })
  } catch (error) {
    console.error('❌ Erro no reset completo:', error)
    res.status(500).json({ error: 'Erro no reset completo' })
  }
})

// Rota para popular com dados de exemplo (opcional)
router.post('/populate-sample-data', async (req, res) => {
  try {
    console.log('🎯 Criando dados de exemplo...')

    // Primeiro, encontrar o usuário admin
    const adminUser = await prisma.user.findUnique({
      where: { username: 'admin' }
    })

    if (!adminUser) {
      return res.status(404).json({ error: 'Usuário admin não encontrado' })
    }

    // Criar um banco de exemplo
    const bank = await prisma.bank.create({
      data: {
        name: 'Banco Exemplo',
        icon: '🏦',
        balance: 5000.00,
        userId: adminUser.id
      }
    })

    // Criar um cartão de exemplo
    await prisma.paymentMethod.create({
      data: {
        name: 'Cartão Exemplo',
        type: 'CREDIT',
        bankId: bank.id,
        userId: adminUser.id
      }
    })

    console.log('✅ Dados de exemplo criados')
    res.json({ message: 'Dados de exemplo criados com sucesso' })
  } catch (error) {
    console.error('❌ Erro ao criar dados de exemplo:', error)
    res.status(500).json({ error: 'Erro ao criar dados de exemplo' })
  }
})

module.exports = router
