import React, { useState, useEffect } from 'react'

function CurrencyInput({ value, onChange, placeholder = "R$ 0,00", className = "", ...props }) {
  const [displayValue, setDisplayValue] = useState('')

  // Formatar valor para exibição
  const formatCurrency = (value) => {
    if (!value) return ''
    
    // Remove tudo que não é número
    const numbers = value.toString().replace(/\D/g, '')
    
    if (!numbers) return ''
    
    // Converte para centavos
    const amount = parseInt(numbers) / 100
    
    // Formata como moeda brasileira
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(amount)
  }

  // Converter valor formatado para número
  const parseValue = (formattedValue) => {
    if (!formattedValue) return 0
    
    // Remove símbolos e converte vírgula para ponto
    const numbers = formattedValue
      .replace(/[^\d,]/g, '')
      .replace(',', '.')
    
    return parseFloat(numbers) || 0
  }

  // Atualizar display quando value prop muda
  useEffect(() => {
    if (value !== undefined) {
      setDisplayValue(formatCurrency(value * 100))
    }
  }, [value])

  const handleChange = (e) => {
    const inputValue = e.target.value
    const formatted = formatCurrency(inputValue)
    const numericValue = parseValue(formatted)
    
    setDisplayValue(formatted)
    
    if (onChange) {
      onChange(numericValue)
    }
  }

  const handleFocus = (e) => {
    // Seleciona todo o texto ao focar
    e.target.select()
  }

  return (
    <input
      type="text"
      value={displayValue}
      onChange={handleChange}
      onFocus={handleFocus}
      placeholder={placeholder}
      className={`text-right ${className}`}
      {...props}
    />
  )
}

export default CurrencyInput
