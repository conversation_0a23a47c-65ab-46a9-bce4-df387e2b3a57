const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Obter dados analíticos por mês
router.get('/', async (req, res) => {
  try {
    const userId = req.user.id;
    const { year = new Date().getFullYear(), month = new Date().getMonth() + 1 } = req.query;

    const startOfMonth = new Date(year, month - 1, 1);
    const endOfMonth = new Date(year, month, 0, 23, 59, 59);

    // Buscar todas as transações do mês
    const transactions = await prisma.transaction.findMany({
      where: {
        userId,
        date: {
          gte: startOfMonth,
          lte: endOfMonth
        }
      },
      include: {
        category: true
      }
    });

    // Buscar todas as categorias do usuário
    const categories = await prisma.category.findMany({
      where: { userId },
      orderBy: { name: 'asc' }
    });

    // Calcular totais por categoria
    const categoryTotals = {};
    categories.forEach(cat => {
      categoryTotals[cat.id] = {
        id: cat.id,
        name: cat.name,
        color: cat.color,
        icon: cat.icon,
        income: 0,
        expense: 0,
        total: 0
      };
    });

    transactions.forEach(t => {
      if (t.categoryId && categoryTotals[t.categoryId]) {
        if (t.type === 'INCOME') {
          categoryTotals[t.categoryId].income += t.amount;
        } else if (t.type === 'EXPENSE') {
          categoryTotals[t.categoryId].expense += t.amount;
        }
        categoryTotals[t.categoryId].total += t.amount;
      }
    });

    // Dados para gráfico de gastos por dia da semana
    const weeklyData = Array.from({ length: 7 }, (_, i) => ({
      day: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'][i],
      dayIndex: i,
      expense: 0,
      income: 0
    }));

    transactions.forEach(t => {
      const dayOfWeek = new Date(t.date).getDay();
      if (t.type === 'EXPENSE') {
        weeklyData[dayOfWeek].expense += t.amount;
      } else if (t.type === 'INCOME') {
        weeklyData[dayOfWeek].income += t.amount;
      }
    });

    // Dados para gráfico de gastos por categoria (apenas despesas)
    const expensesByCategory = Object.values(categoryTotals)
      .filter(cat => cat.expense > 0)
      .map(cat => ({
        name: cat.name,
        value: cat.expense,
        color: cat.color,
        icon: cat.icon
      }));

    // Dados para gráfico de receitas por categoria
    const incomeByCategory = Object.values(categoryTotals)
      .filter(cat => cat.income > 0)
      .map(cat => ({
        name: cat.name,
        value: cat.income,
        color: cat.color,
        icon: cat.icon
      }));

    // Dados diários do mês
    const daysInMonth = new Date(year, month, 0).getDate();
    const dailyData = Array.from({ length: daysInMonth }, (_, i) => {
      const day = i + 1;
      const dayTransactions = transactions.filter(t => 
        new Date(t.date).getDate() === day
      );
      
      return {
        day,
        expense: dayTransactions
          .filter(t => t.type === 'EXPENSE')
          .reduce((sum, t) => sum + t.amount, 0),
        income: dayTransactions
          .filter(t => t.type === 'INCOME')
          .reduce((sum, t) => sum + t.amount, 0)
      };
    });

    // Totais do mês
    const totalIncome = transactions
      .filter(t => t.type === 'INCOME')
      .reduce((sum, t) => sum + t.amount, 0);

    const totalExpenses = transactions
      .filter(t => t.type === 'EXPENSE')
      .reduce((sum, t) => sum + t.amount, 0);

    const totalInvestments = transactions
      .filter(t => t.type === 'INVESTMENT')
      .reduce((sum, t) => sum + t.amount, 0);

    const totalLoans = transactions
      .filter(t => t.type === 'LOAN')
      .reduce((sum, t) => sum + t.amount, 0);

    res.json({
      summary: {
        totalIncome,
        totalExpenses,
        totalInvestments,
        totalLoans,
        balance: totalIncome - totalExpenses,
        transactionCount: transactions.length
      },
      charts: {
        expensesByCategory,
        incomeByCategory,
        weeklyData,
        dailyData
      },
      categories: Object.values(categoryTotals),
      period: {
        year: parseInt(year),
        month: parseInt(month),
        monthName: new Date(year, month - 1).toLocaleDateString('pt-BR', { month: 'long' })
      }
    });
  } catch (error) {
    console.error('Erro ao buscar dados analíticos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
