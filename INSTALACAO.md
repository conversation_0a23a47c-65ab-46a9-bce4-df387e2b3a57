# 📋 Guia de Instalação - Sistema Sara

## ⚠️ Pré-requisitos

### 1. Instalar Node.js
1. Acesse https://nodejs.org/
2. Baixe a versão LTS (recomendada)
3. Execute o instalador e siga as instruções
4. Reinicie o terminal/PowerShell após a instalação

### 2. Verificar instalação
Abra o PowerShell ou CMD e execute:
```bash
node --version
npm --version
```

Se os comandos retornarem as versões, está tudo certo!

## 🚀 Instalação do Sistema

### 1. Instalar dependências do backend
```bash
cd backend
npm install
```

### 2. Instalar dependências do frontend
```bash
cd ../frontend
npm install
```

### 3. Configurar banco de dados
```bash
cd ../backend
npx prisma generate
npx prisma db push
```

**Se der erro de enum:** Execute o script de correção:
```bash
cd ..
fix-database.bat
```

### 4. Popular banco com dados de exemplo (opcional)
```bash
node src/seed.js
```

## ▶️ Executar o sistema

### Opção 1: Executar tudo junto (recomendado)
Na raiz do projeto:
```bash
npm install
npm run dev
```

### Opção 2: Executar separadamente

#### Terminal 1 - Backend:
```bash
cd backend
npm run dev
```

#### Terminal 2 - Frontend:
```bash
cd frontend
npm run dev
```

## 🌐 Acessar o sistema

1. Abra o navegador
2. Acesse: http://localhost:5173
3. Use as credenciais:
   - **Email:** <EMAIL>
   - **Senha:** 123456

## 🔧 Solução de problemas

### Erro "npm não é reconhecido"
- Instale o Node.js conforme instruções acima
- Reinicie o terminal
- Verifique se o Node.js foi adicionado ao PATH

### Erro de porta em uso
- Mude a porta no arquivo `backend/.env` (PORT=3002)
- Ou mate o processo que está usando a porta

### Erro de banco de dados / Erro de enum
```bash
# Execute o script de correção
fix-database.bat

# Ou manualmente:
cd backend
del dev.db
npx prisma generate
npx prisma db push
node src/seed.js
```

### Erro de CSS / Tailwind
```bash
# Execute o script de correção do frontend
fix-frontend.bat
```

### Erro de dependências
```bash
# Limpar cache e reinstalar
rm -rf node_modules package-lock.json
npm install
```

## 📱 Funcionalidades disponíveis

✅ Login/Registro de usuários
✅ Dashboard com gráficos
✅ Gestão de transações
✅ Categorias personalizáveis
✅ Filtros e busca
✅ Design responsivo

## 🆘 Suporte

Se encontrar problemas:
1. Verifique se o Node.js está instalado
2. Verifique se as portas 3001 e 5173 estão livres
3. Consulte os logs no terminal para erros específicos
