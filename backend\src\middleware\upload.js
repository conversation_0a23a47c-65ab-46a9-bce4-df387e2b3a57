const multer = require('multer');
const cloudinary = require('../config/cloudinary');
const { Readable } = require('stream');

// Configurar multer para armazenar arquivos em memória
const storage = multer.memoryStorage();

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    // Aceitar apenas imagens e PDFs
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Tipo de arquivo não permitido. Apenas JPEG, PNG e PDF são aceitos.'), false);
    }
  }
});

// Função para fazer upload para o Cloudinary com timeout
const uploadToCloudinary = async (buffer, originalname, mimetype) => {
  return new Promise((resolve, reject) => {
    // Timeout de 30 segundos
    const timeout = setTimeout(() => {
      reject(new Error('Upload timeout - operação cancelada após 30 segundos'));
    }, 30000);

    const uploadStream = cloudinary.uploader.upload_stream(
      {
        resource_type: mimetype.startsWith('image/') ? 'image' : 'raw',
        folder: 'transaction-receipts',
        public_id: `receipt_${Date.now()}_${originalname.split('.')[0]}`,
        format: mimetype === 'application/pdf' ? 'pdf' : undefined,
        timeout: 30000 // 30 segundos
      },
      (error, result) => {
        clearTimeout(timeout);
        if (error) {
          console.error('Erro no upload do Cloudinary:', error);
          reject(error);
        } else {
          resolve(result);
        }
      }
    );

    try {
      // Converter buffer para stream e fazer upload
      const stream = Readable.from(buffer);
      stream.pipe(uploadStream);

      // Tratar erros do stream
      stream.on('error', (error) => {
        clearTimeout(timeout);
        console.error('Erro no stream de upload:', error);
        reject(error);
      });

      uploadStream.on('error', (error) => {
        clearTimeout(timeout);
        console.error('Erro no uploadStream:', error);
        reject(error);
      });
    } catch (error) {
      clearTimeout(timeout);
      console.error('Erro ao criar stream:', error);
      reject(error);
    }
  });
};

module.exports = {
  upload,
  uploadToCloudinary
};
