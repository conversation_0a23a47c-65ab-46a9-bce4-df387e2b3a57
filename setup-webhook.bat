@echo off
echo ========================================
echo    🎯 Setup Auto-Deploy Webhook
echo    Configurando deploy automatico...
echo ========================================
echo.

echo ⏳ Verificando Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js nao encontrado!
    echo Instale o Node.js primeiro
    pause
    exit /b 1
)

echo ⏳ Verificando PM2...
pm2 --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 Instalando PM2...
    npm install -g pm2
    if %errorlevel% neq 0 (
        echo ❌ Erro ao instalar PM2
        pause
        exit /b 1
    )
)

echo ⏳ Criando arquivo de configuracao do webhook...
if not exist webhook.env (
    echo # Configuracao do Webhook > webhook.env
    echo WEBHOOK_PORT=9000 >> webhook.env
    echo WEBHOOK_SECRET=your-webhook-secret-change-this >> webhook.env
    echo DEPLOY_BRANCH=develop >> webhook.env
    echo NODE_ENV=production >> webhook.env
    echo.
    echo ⚠️  Configure o arquivo webhook.env com suas configuracoes
    echo    Especialmente o WEBHOOK_SECRET que deve ser o mesmo do GitHub
)

echo ⏳ Criando arquivo de configuracao do PM2...
(
echo {
echo   "name": "sara-webhook",
echo   "script": "deploy-webhook.js",
echo   "env_file": "webhook.env",
echo   "instances": 1,
echo   "autorestart": true,
echo   "watch": false,
echo   "max_memory_restart": "1G",
echo   "log_file": "logs/webhook.log",
echo   "out_file": "logs/webhook-out.log",
echo   "error_file": "logs/webhook-error.log",
echo   "log_date_format": "YYYY-MM-DD HH:mm:ss Z"
echo }
) > ecosystem.config.json

echo ⏳ Criando diretorio de logs...
if not exist logs mkdir logs

echo ⏳ Instalando dependencias do webhook...
npm init -y >nul 2>&1
npm install express >nul 2>&1

echo ⏳ Iniciando webhook com PM2...
pm2 start ecosystem.config.json
if %errorlevel% neq 0 (
    echo ❌ Erro ao iniciar webhook
    pause
    exit /b 1
)

echo ⏳ Configurando PM2 para iniciar automaticamente...
pm2 startup
pm2 save

echo.
echo ✅ Webhook configurado com sucesso!
echo.
echo 📋 Proximos passos:
echo.
echo 1. Configure o arquivo webhook.env:
echo    - WEBHOOK_SECRET: mesmo valor do GitHub webhook
echo    - WEBHOOK_PORT: porta do webhook (padrao: 9000)
echo    - DEPLOY_BRANCH: branch para deploy (padrao: develop)
echo.
echo 2. Configure o webhook no GitHub:
echo    - URL: http://seu-servidor:9000/webhook
echo    - Content type: application/json
echo    - Secret: mesmo valor do WEBHOOK_SECRET
echo    - Events: Just the push event
echo.
echo 3. Comandos uteis:
echo    pm2 status          - Ver status dos processos
echo    pm2 logs sara-webhook - Ver logs do webhook
echo    pm2 restart sara-webhook - Reiniciar webhook
echo    pm2 stop sara-webhook - Parar webhook
echo.
echo 🌐 Endpoints disponiveis:
echo    http://localhost:9000/health - Health check
echo    http://localhost:9000/status - Status do deploy
echo.
pause
