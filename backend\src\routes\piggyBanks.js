const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticateToken);

// Listar cofrinhos
router.get('/', async (req, res) => {
  try {
    const piggyBanks = await prisma.piggyBank.findMany({
      where: { userId: req.user.id },
      include: {
        bank: true
      },
      orderBy: { createdAt: 'desc' }
    });

    res.json(piggyBanks);
  } catch (error) {
    console.error('Erro ao buscar cofrinhos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar cofrinho
router.post('/', async (req, res) => {
  try {
    const { name, description, currentAmount, targetAmount, bankId, cdiRate, icon, color } = req.body;

    if (!name || !bankId) {
      return res.status(400).json({ error: 'Nome e banco são obrigatórios' });
    }

    // Verificar se o banco existe e pertence ao usuário
    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    const piggyBank = await prisma.piggyBank.create({
      data: {
        name,
        description: description || null,
        currentAmount: parseFloat(currentAmount) || 0,
        targetAmount: targetAmount ? parseFloat(targetAmount) : null,
        bankId,
        cdiRate: parseFloat(cdiRate) || 0,
        icon: icon || '🐷',
        color: color || '#EC4899',
        userId: req.user.id
      },
      include: {
        bank: true
      }
    });

    res.status(201).json(piggyBank);
  } catch (error) {
    console.error('Erro ao criar cofrinho:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar cofrinho
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, currentAmount, targetAmount, cdiRate, isActive, icon, color } = req.body;

    const piggyBank = await prisma.piggyBank.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!piggyBank) {
      return res.status(404).json({ error: 'Cofrinho não encontrado' });
    }

    const updatedPiggyBank = await prisma.piggyBank.update({
      where: { id },
      data: {
        name: name || piggyBank.name,
        description: description !== undefined ? description : piggyBank.description,
        currentAmount: currentAmount !== undefined ? parseFloat(currentAmount) : piggyBank.currentAmount,
        targetAmount: targetAmount !== undefined ? (targetAmount ? parseFloat(targetAmount) : null) : piggyBank.targetAmount,
        cdiRate: cdiRate !== undefined ? parseFloat(cdiRate) : piggyBank.cdiRate,
        isActive: isActive !== undefined ? isActive : piggyBank.isActive,
        icon: icon || piggyBank.icon,
        color: color || piggyBank.color,
        lastCdiUpdate: cdiRate !== undefined ? new Date() : piggyBank.lastCdiUpdate
      },
      include: {
        bank: true
      }
    });

    res.json(updatedPiggyBank);
  } catch (error) {
    console.error('Erro ao atualizar cofrinho:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar cofrinho
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const piggyBank = await prisma.piggyBank.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!piggyBank) {
      return res.status(404).json({ error: 'Cofrinho não encontrado' });
    }

    await prisma.piggyBank.delete({
      where: { id }
    });

    res.json({ message: 'Cofrinho excluído com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir cofrinho:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Adicionar valor ao cofrinho
router.post('/:id/add', async (req, res) => {
  try {
    const { id } = req.params;
    const { amount } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({ error: 'Valor deve ser maior que zero' });
    }

    const piggyBank = await prisma.piggyBank.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!piggyBank) {
      return res.status(404).json({ error: 'Cofrinho não encontrado' });
    }

    const updatedPiggyBank = await prisma.piggyBank.update({
      where: { id },
      data: {
        currentAmount: piggyBank.currentAmount + parseFloat(amount)
      },
      include: {
        bank: true
      }
    });

    res.json(updatedPiggyBank);
  } catch (error) {
    console.error('Erro ao adicionar valor ao cofrinho:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Retirar valor do cofrinho
router.post('/:id/withdraw', async (req, res) => {
  try {
    const { id } = req.params;
    const { amount } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({ error: 'Valor deve ser maior que zero' });
    }

    const piggyBank = await prisma.piggyBank.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!piggyBank) {
      return res.status(404).json({ error: 'Cofrinho não encontrado' });
    }

    if (piggyBank.currentAmount < parseFloat(amount)) {
      return res.status(400).json({ error: 'Saldo insuficiente no cofrinho' });
    }

    const updatedPiggyBank = await prisma.piggyBank.update({
      where: { id },
      data: {
        currentAmount: piggyBank.currentAmount - parseFloat(amount)
      },
      include: {
        bank: true
      }
    });

    res.json(updatedPiggyBank);
  } catch (error) {
    console.error('Erro ao retirar valor do cofrinho:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar CDI de todos os cofrinhos
router.post('/update-cdi', async (req, res) => {
  try {
    const { cdiRate } = req.body;

    if (!cdiRate || cdiRate < 0) {
      return res.status(400).json({ error: 'Taxa CDI deve ser maior ou igual a zero' });
    }

    // Atualizar todos os cofrinhos do usuário
    const updatedCount = await prisma.piggyBank.updateMany({
      where: { 
        userId: req.user.id,
        isActive: true
      },
      data: {
        cdiRate: parseFloat(cdiRate),
        lastCdiUpdate: new Date()
      }
    });

    res.json({ 
      message: `CDI atualizado para ${updatedCount.count} cofrinhos`,
      updatedCount: updatedCount.count,
      newCdiRate: parseFloat(cdiRate)
    });
  } catch (error) {
    console.error('Erro ao atualizar CDI:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Aplicar rendimento CDI
router.post('/apply-cdi', async (req, res) => {
  try {
    const piggyBanks = await prisma.piggyBank.findMany({
      where: { 
        userId: req.user.id,
        isActive: true,
        cdiRate: { gt: 0 }
      }
    });

    let updatedCount = 0;

    for (const piggyBank of piggyBanks) {
      // Calcular rendimento diário baseado no CDI
      const dailyRate = piggyBank.cdiRate / 365 / 100;
      const dailyYield = piggyBank.currentAmount * dailyRate;
      
      await prisma.piggyBank.update({
        where: { id: piggyBank.id },
        data: {
          currentAmount: piggyBank.currentAmount + dailyYield
        }
      });

      updatedCount++;
    }

    res.json({ 
      message: `Rendimento aplicado em ${updatedCount} cofrinhos`,
      updatedCount
    });
  } catch (error) {
    console.error('Erro ao aplicar rendimento CDI:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
