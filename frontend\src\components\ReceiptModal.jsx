import React, { useState } from 'react'
import { X, Download, Eye, FileText, Image } from 'lucide-react'
import toast from 'react-hot-toast'

function ReceiptModal({ isOpen, onClose, receiptUrl, transactionDescription }) {
  const [loading, setLoading] = useState(false)

  if (!isOpen) return null

  const isImage = receiptUrl?.includes('image') || receiptUrl?.match(/\.(jpg|jpeg|png|gif|webp)$/i)
  const isPdf = receiptUrl?.includes('pdf') || receiptUrl?.match(/\.pdf$/i)

  const handleDownload = async () => {
    try {
      setLoading(true)
      
      // Fazer download do arquivo
      const response = await fetch(receiptUrl)
      const blob = await response.blob()
      
      // Criar URL temporária para download
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      
      // Definir nome do arquivo
      const extension = isPdf ? 'pdf' : 'jpg'
      const fileName = `comprovante_${transactionDescription?.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}.${extension}`
      link.download = fileName
      
      // Executar download
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // Limpar URL temporária
      window.URL.revokeObjectURL(url)
      
      toast.success('Download realizado com sucesso!')
    } catch (error) {
      console.error('Erro ao fazer download:', error)
      toast.error('Erro ao fazer download do arquivo')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-slate-800 to-slate-900 text-white p-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              {isImage ? <Image className="h-5 w-5" /> : <FileText className="h-5 w-5" />}
            </div>
            <div>
              <h2 className="text-lg font-semibold">Comprovante da Transação</h2>
              <p className="text-slate-300 text-sm">{transactionDescription}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={handleDownload}
              disabled={loading}
              className="flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Download className="h-4 w-4" />
              )}
              <span className="text-sm">Download</span>
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[calc(90vh-120px)] overflow-auto">
          {isImage ? (
            <div className="flex justify-center">
              <img
                src={receiptUrl}
                alt="Comprovante"
                className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
                onError={(e) => {
                  e.target.style.display = 'none'
                  toast.error('Erro ao carregar imagem')
                }}
              />
            </div>
          ) : isPdf ? (
            <div className="w-full h-96 border rounded-lg overflow-hidden">
              <iframe
                src={`${receiptUrl}#toolbar=0&navpanes=0&scrollbar=0`}
                className="w-full h-full"
                title="Comprovante PDF"
                onError={() => {
                  toast.error('Erro ao carregar PDF')
                }}
              />
              <div className="text-center p-4 bg-gray-50">
                <p className="text-gray-600 text-sm mb-2">
                  Não consegue visualizar o PDF? 
                </p>
                <button
                  onClick={handleDownload}
                  className="text-blue-600 hover:text-blue-800 underline text-sm"
                >
                  Clique aqui para fazer download
                </button>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Arquivo não suportado para visualização
              </h3>
              <p className="text-gray-600 mb-4">
                Este tipo de arquivo não pode ser visualizado no navegador
              </p>
              <button
                onClick={handleDownload}
                disabled={loading}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mx-auto"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Download className="h-4 w-4" />
                )}
                <span>Fazer Download</span>
              </button>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center gap-4">
              <span>Tipo: {isImage ? 'Imagem' : isPdf ? 'PDF' : 'Arquivo'}</span>
              <span>•</span>
              <span>Clique em Download para salvar o arquivo</span>
            </div>
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Fechar
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ReceiptModal
