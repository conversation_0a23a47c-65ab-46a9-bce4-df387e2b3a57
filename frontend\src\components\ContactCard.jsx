import React from 'react'
import { Eye, Edit, Plus, User, Phone, Mail, AlertCircle, CheckCircle, Clock, Trash2, DollarSign } from 'lucide-react'
import { loanUtils } from '../services/loanService'

function ContactCard({ contact, onView, onEdit, onCreateLoan, onDelete }) {
  const hasActiveLoans = contact.loans && contact.loans.length > 0
  const activeLoansCount = contact.loans?.filter(loan => loan.type === 'LOAN_GIVEN').length || 0
  const receivedLoansCount = contact.loans?.filter(loan => loan.type === 'LOAN_RECEIVED').length || 0

  const getStatusIcon = () => {
    switch (contact.status) {
      case 'GOOD':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'BAD':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'GOOD':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'BAD':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusColor = () => {
    switch (contact.status) {
      case 'GOOD':
        return 'bg-green-100 text-green-800'
      case 'BAD':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div
      onClick={onView}
      className="bg-white rounded-xl border border-gray-200 p-4 hover:border-blue-500 transition-colors cursor-pointer"
    >
      <div className="flex items-center gap-4">
        {/* Foto do Contato */}
        <div className="relative">
          {contact.photoUrl ? (
            <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-gray-200">
              <img
                src={contact.photoUrl}
                alt={contact.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.target.onerror = null
                  e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(contact.name)}&background=random&size=48&bold=true&color=fff`
                }}
                loading="lazy"
              />
            </div>
          ) : (
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center border-2 border-gray-200">
              <User className="h-6 w-6 text-white" />
            </div>
          )}
          
          {/* Status Badge */}
          <div className={`absolute -bottom-1 -right-1 rounded-full p-1 ${getStatusColor()}`}>
            {getStatusIcon()}
          </div>
        </div>

        {/* Informações do Contato */}
        <div className="flex-1 min-w-0">
          <h3 className="text-gray-900 font-medium truncate">{contact.name}</h3>
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <DollarSign className="h-4 w-4" />
            <span>{contact.loans?.length || 0} empréstimos</span>
          </div>
        </div>
      </div>

      {/* Informações de contato */}
      <div className="space-y-2 mb-4">
        {contact.email && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Mail className="h-4 w-4" />
            <span className="truncate">{contact.email}</span>
          </div>
        )}
        {contact.phone && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Phone className="h-4 w-4" />
            <span>{contact.phone}</span>
          </div>
        )}
      </div>

      {/* Status de empréstimos */}
      <div className="mb-4">
        {hasActiveLoans ? (
          <div className="space-y-2">
            {activeLoansCount > 0 && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Emprestei para:</span>
                <span className="font-medium text-red-600">{activeLoansCount} empréstimo(s)</span>
              </div>
            )}
            {receivedLoansCount > 0 && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Peguei de:</span>
                <span className="font-medium text-green-600">{receivedLoansCount} empréstimo(s)</span>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-2">
            <p className="text-sm text-gray-500">Nenhum empréstimo ativo</p>
          </div>
        )}
      </div>

      {/* Estatísticas de pagamento */}
      {contact.totalLoans > 0 && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <p className="text-xs text-gray-600">Em dia</p>
              <p className="text-sm font-semibold text-green-600">{contact.paidOnTime}</p>
            </div>
            <div>
              <p className="text-xs text-gray-600">Atrasados</p>
              <p className="text-sm font-semibold text-red-600">{contact.latePayments}</p>
            </div>
          </div>
        </div>
      )}

      {/* Ações */}
      <div className="flex gap-1">
        <button
          onClick={onEdit}
          className="flex items-center justify-center p-2 text-sm bg-gray-50 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
          title="Editar contato"
        >
          <Edit className="h-4 w-4" />
        </button>

        <button
          onClick={onCreateLoan}
          className="flex items-center justify-center p-2 text-sm bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors"
          title="Novo empréstimo"
        >
          <Plus className="h-4 w-4" />
        </button>

        <button
          onClick={onDelete}
          className="flex items-center justify-center p-2 text-sm bg-red-50 text-red-700 rounded-lg hover:bg-red-100 transition-colors"
          title="Deletar contato"
        >
          <Trash2 className="h-4 w-4" />
        </button>
      </div>

      {/* Notas (se houver) */}
      {contact.notes && (
        <div className="mt-3 pt-3 border-t border-gray-100">
          <p className="text-xs text-gray-600 line-clamp-2">
            {contact.notes}
          </p>
        </div>
      )}
    </div>
  )
}

export default ContactCard
