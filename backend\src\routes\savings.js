const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Listar cofrinhos do usuário
router.get('/', async (req, res) => {
  try {
    const savings = await prisma.savings.findMany({
      where: { userId: req.user.id },
      include: {
        bank: true
      },
      orderBy: { name: 'asc' }
    });

    res.json(savings);
  } catch (error) {
    console.error('Erro ao buscar cofrinhos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar cofrinho
router.post('/', async (req, res) => {
  try {
    const { name, description, targetAmount, icon, color, bankId } = req.body;

    if (!name || !bankId) {
      return res.status(400).json({ error: 'Nome e banco são obrigatórios' });
    }

    // Verificar se o banco existe e pertence ao usuário
    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    const savings = await prisma.savings.create({
      data: {
        name,
        description: description || null,
        targetAmount: targetAmount || 0,
        icon: icon || '🐷',
        color: color || '#22C55E',
        bankId,
        userId: req.user.id
      },
      include: {
        bank: true
      }
    });

    res.status(201).json(savings);
  } catch (error) {
    console.error('Erro ao criar cofrinho:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar cofrinho
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, targetAmount, icon, color, isLocked } = req.body;

    const savings = await prisma.savings.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!savings) {
      return res.status(404).json({ error: 'Cofrinho não encontrado' });
    }

    const updatedSavings = await prisma.savings.update({
      where: { id },
      data: {
        name: name || savings.name,
        description: description !== undefined ? description : savings.description,
        targetAmount: targetAmount !== undefined ? targetAmount : savings.targetAmount,
        icon: icon || savings.icon,
        color: color || savings.color,
        isLocked: isLocked !== undefined ? isLocked : savings.isLocked
      },
      include: {
        bank: true
      }
    });

    res.json(updatedSavings);
  } catch (error) {
    console.error('Erro ao atualizar cofrinho:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar cofrinho
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const savings = await prisma.savings.findFirst({
      where: { id, userId: req.user.id },
      include: { bank: true }
    });

    if (!savings) {
      return res.status(404).json({ error: 'Cofrinho não encontrado' });
    }

    // Se há dinheiro no cofrinho, devolver para o banco
    if (savings.currentAmount > 0) {
      await prisma.bank.update({
        where: { id: savings.bankId },
        data: {
          currentBalance: savings.bank.currentBalance + savings.currentAmount
        }
      });
    }

    await prisma.savings.delete({
      where: { id }
    });

    res.json({ message: 'Cofrinho excluído com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir cofrinho:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Depositar no cofrinho
router.post('/:id/deposit', async (req, res) => {
  try {
    const { id } = req.params;
    const { amount, bankId } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({ error: 'Valor deve ser maior que zero' });
    }

    if (!bankId) {
      return res.status(400).json({ error: 'Banco é obrigatório' });
    }

    const savings = await prisma.savings.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!savings) {
      return res.status(404).json({ error: 'Cofrinho não encontrado' });
    }

    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    if (bank.currentBalance < amount) {
      return res.status(400).json({ error: 'Saldo insuficiente no banco' });
    }

    // Transferir dinheiro do banco para o cofrinho
    await prisma.$transaction(async (tx) => {
      // Debitar do banco
      await tx.bank.update({
        where: { id: bankId },
        data: { currentBalance: bank.currentBalance - amount }
      });

      // Creditar no cofrinho
      await tx.savings.update({
        where: { id },
        data: { currentAmount: savings.currentAmount + amount }
      });
    });

    res.json({ message: 'Depósito realizado com sucesso' });
  } catch (error) {
    console.error('Erro ao depositar no cofrinho:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Sacar do cofrinho
router.post('/:id/withdraw', async (req, res) => {
  try {
    const { id } = req.params;
    const { amount, bankId } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({ error: 'Valor deve ser maior que zero' });
    }

    if (!bankId) {
      return res.status(400).json({ error: 'Banco é obrigatório' });
    }

    const savings = await prisma.savings.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!savings) {
      return res.status(404).json({ error: 'Cofrinho não encontrado' });
    }

    if (savings.isLocked) {
      return res.status(400).json({ error: 'Cofrinho está bloqueado' });
    }

    if (savings.currentAmount < amount) {
      return res.status(400).json({ error: 'Saldo insuficiente no cofrinho' });
    }

    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    // Transferir dinheiro do cofrinho para o banco
    await prisma.$transaction(async (tx) => {
      // Debitar do cofrinho
      await tx.savings.update({
        where: { id },
        data: { currentAmount: savings.currentAmount - amount }
      });

      // Creditar no banco
      await tx.bank.update({
        where: { id: bankId },
        data: { currentBalance: bank.currentBalance + amount }
      });
    });

    res.json({ message: 'Saque realizado com sucesso' });
  } catch (error) {
    console.error('Erro ao sacar do cofrinho:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
