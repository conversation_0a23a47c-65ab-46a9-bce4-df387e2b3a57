import React, { useState, useEffect } from 'react'
import { X, DollarSign, Calendar, Building2, Upload, FileText } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { loanService, loanUtils } from '../services/loanService'
import toast from 'react-hot-toast'

function PaymentModal({ isOpen, onClose, loan, payment, banks, onSuccess }) {
  const [loading, setLoading] = useState(false)
  const [receiptFile, setReceiptFile] = useState(null)

  const { register, handleSubmit, reset, formState: { errors } } = useForm()

  useEffect(() => {
    if (isOpen && payment) {
      reset({
        amount: payment.amount,
        paymentDate: new Date().toISOString().split('T')[0],
        bankId: '',
        notes: ''
      })
      setReceiptFile(null)
    }
  }, [isOpen, payment, reset])

  const handleReceiptChange = (e) => {
    const file = e.target.files[0]
    if (file) {
      if (file.size > 10 * 1024 * 1024) { // 10MB
        toast.error('Arquivo muito grande. Máximo 10MB.')
        return
      }
      setReceiptFile(file)
    }
  }

  const onSubmit = async (data) => {
    try {
      setLoading(true)

      const formData = {
        paymentId: payment.id,
        amount: parseFloat(data.amount),
        paymentDate: data.paymentDate,
        bankId: data.bankId || null,
        notes: data.notes || null
      }

      if (receiptFile) {
        formData.receipt = receiptFile
      }

      await loanService.registerPayment(loan.id, formData)
      toast.success('Pagamento registrado com sucesso!')
      
      onSuccess()
      onClose()
    } catch (error) {
      console.error('Erro ao registrar pagamento:', error)
      toast.error('Erro ao registrar pagamento')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen || !payment || !loan) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-green-600 to-green-700 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <DollarSign className="h-5 w-5" />
              </div>
              <div>
                <h2 className="text-xl font-bold">Registrar Pagamento</h2>
                <p className="text-green-100 text-sm">{loan.title}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
          {/* Informações da Parcela */}
          <div className="bg-gray-50 rounded-xl p-4">
            <h3 className="font-medium text-gray-900 mb-3">Detalhes da Parcela</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Vencimento:</span>
                <span className="font-medium">{loanUtils.formatDate(payment.dueDate)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Valor Original:</span>
                <span className="font-medium">{loanUtils.formatCurrency(payment.amount)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className={`font-medium ${
                  loanUtils.isOverdue(payment.dueDate, false) ? 'text-red-600' : 'text-blue-600'
                }`}>
                  {loanUtils.isOverdue(payment.dueDate, false) ? 'Em Atraso' : 'No Prazo'}
                </span>
              </div>
            </div>
          </div>

          {/* Valor Pago */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Valor Pago *
            </label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="number"
                step="0.01"
                {...register('amount', { 
                  required: 'Valor é obrigatório',
                  min: { value: 0.01, message: 'Valor deve ser maior que zero' }
                })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="0,00"
              />
            </div>
            {errors.amount && (
              <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
            )}
          </div>

          {/* Data do Pagamento */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Data do Pagamento *
            </label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="date"
                {...register('paymentDate', { required: 'Data é obrigatória' })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            {errors.paymentDate && (
              <p className="mt-1 text-sm text-red-600">{errors.paymentDate.message}</p>
            )}
          </div>

          {/* Banco */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Banco *
            </label>
            <div className="relative">
              <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <select
                {...register('bankId', { required: 'Selecione um banco' })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="">Selecione o banco</option>
                {banks.map((bank) => (
                  <option key={bank.id} value={bank.id}>
                    {bank.icon} {bank.name}
                  </option>
                ))}
              </select>
            </div>
            {errors.bankId && (
              <p className="mt-1 text-sm text-red-600">{errors.bankId.message}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Uma transação será criada automaticamente neste banco
            </p>
          </div>

          {/* Comprovante */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Comprovante
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-green-400 transition-colors">
              <input
                type="file"
                onChange={handleReceiptChange}
                className="hidden"
                id="payment-receipt-upload"
                accept="image/*,.pdf"
              />
              <label htmlFor="payment-receipt-upload" className="cursor-pointer">
                <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">
                  {receiptFile ? receiptFile.name : 'Clique para adicionar comprovante'}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  PNG, JPG ou PDF até 10MB
                </p>
              </label>
            </div>
          </div>

          {/* Observações */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Observações
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
              <textarea
                {...register('notes')}
                rows={3}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
                placeholder="Informações adicionais sobre o pagamento..."
              />
            </div>
          </div>

          {/* Aviso sobre atraso */}
          {loanUtils.isOverdue(payment.dueDate, false) && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">
                    Pagamento em Atraso
                  </p>
                  <p className="text-xs text-yellow-700">
                    Este pagamento será marcado como atrasado e pode afetar o status do contato.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Botões */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Registrando...
                </>
              ) : (
                'Registrar Pagamento'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default PaymentModal
