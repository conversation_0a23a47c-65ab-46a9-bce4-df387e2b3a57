import React, { useState } from 'react'
import { Menu, ChevronDown } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { useLocation } from 'react-router-dom'

function Header({ onMenuClick, selectedYear, onYearChange, availableYears = [] }) {
  const { user } = useAuth()
  const location = useLocation()
  const [showYearDropdown, setShowYearDropdown] = useState(false)
  const [yearSearch, setYearSearch] = useState('')

  // Definir título baseado na rota
  const getPageTitle = () => {
    switch (location.pathname) {
      case '/':
      case '/dashboard':
        return 'Página Inicial'
      case '/dashboard-analytics':
        return 'Dashboard Analytics'
      case '/transactions':
        return 'Transações'
      case '/banks':
        return 'Bancos e Pagamentos'
      case '/categories':
        return 'Categorias'
      case '/loans':
        return 'Empréstimos e Dívidas'
      case '/reports':
        return 'Relató<PERSON>s'
      case '/settings':
        return 'Configurações'
      default:
        return 'Sara - Sistema Financeiro'
    }
  }

  // Verificar se deve mostrar o filtro de ano
  const shouldShowYearFilter = location.pathname === '/' || location.pathname === '/dashboard' || location.pathname === '/dashboard-analytics'

  // Usar anos disponíveis das transações ou gerar anos padrão
  const currentYear = new Date().getFullYear()
  const years = availableYears.length > 0
    ? availableYears.sort((a, b) => b - a)
    : Array.from({ length: currentYear - 2019 + 2 }, (_, i) => currentYear + 1 - i)

  // Filtrar anos baseado na busca
  const filteredYears = years.filter(year =>
    year.toString().includes(yearSearch)
  )

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left side - Menu button and title */}
        <div className="flex items-center">
          <button
            onClick={onMenuClick}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
          >
            <Menu className="h-6 w-6" />
          </button>

          <div className="ml-4 lg:ml-0">
            <h1 className="text-2xl font-semibold text-gray-900">
              {getPageTitle()}
            </h1>
          </div>
        </div>

        {/* Right side - Year selector (apenas na página inicial e dashboard) */}
        <div className="flex items-center space-x-4">
          {shouldShowYearFilter && (
            <div className="relative">
              <button
                onClick={() => setShowYearDropdown(!showYearDropdown)}
                className="flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-md"
              >
                <span className="font-medium">{selectedYear || currentYear}</span>
                <ChevronDown className="ml-2 h-4 w-4" />
              </button>

              {showYearDropdown && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-xl border border-gray-200 z-10 overflow-hidden">
                  <div className="p-3 border-b border-gray-200">
                    <input
                      type="text"
                      placeholder="Buscar ano..."
                      value={yearSearch}
                      onChange={(e) => setYearSearch(e.target.value)}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div className="py-1 max-h-48 overflow-y-auto">
                    {filteredYears.length > 0 ? (
                      filteredYears.map((year) => (
                        <button
                          key={year}
                          onClick={() => {
                            if (onYearChange) {
                              onYearChange(year)
                            }
                            setShowYearDropdown(false)
                            setYearSearch('')
                          }}
                          className={`block w-full text-left px-4 py-3 text-sm hover:bg-blue-50 transition-colors ${
                            year === (selectedYear || currentYear)
                              ? 'bg-blue-50 font-semibold text-blue-700 border-r-4 border-blue-500'
                              : 'text-gray-700'
                          }`}
                        >
                          {year}
                          {availableYears.includes(year) && (
                            <span className="ml-2 text-xs text-green-600">• Com dados</span>
                          )}
                        </button>
                      ))
                    ) : (
                      <div className="px-4 py-3 text-sm text-gray-500">
                        Nenhum ano encontrado
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </header>
  )
}

export default Header
