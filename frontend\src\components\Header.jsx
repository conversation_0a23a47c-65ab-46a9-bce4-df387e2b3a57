import React, { useState } from 'react'
import { Menu, ChevronDown } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { useLocation } from 'react-router-dom'

function Header({ onMenuClick, selectedYear, onYearChange, availableYears = [] }) {
  const { user } = useAuth()
  const location = useLocation()
  const [showYearDropdown, setShowYearDropdown] = useState(false)
  const [yearSearch, setYearSearch] = useState('')

  // Definir título baseado na rota
  const getPageTitle = () => {
    switch (location.pathname) {
      case '/':
      case '/dashboard':
        return 'Página Inicial'
      case '/dashboard-analytics':
        return 'Dashboard Analytics'
      case '/transactions':
        return 'Transações'
      case '/banks':
        return 'Bancos e Pagamentos'
      case '/categories':
        return 'Categorias'
      case '/loans':
        return 'Empréstimos e Dívidas'
      case '/reports':
        return 'Relató<PERSON>s'
      case '/settings':
        return 'Configurações'
      default:
        return 'Sara - Sistema Financeiro'
    }
  }

  // Verificar se deve mostrar o filtro de ano
  const shouldShowYearFilter = location.pathname === '/' || location.pathname === '/dashboard' || location.pathname === '/dashboard-analytics'

  // Usar anos disponíveis das transações ou gerar anos padrão
  const currentYear = new Date().getFullYear()
  const years = availableYears.length > 0
    ? availableYears.sort((a, b) => b - a)
    : Array.from({ length: currentYear - 2019 + 2 }, (_, i) => currentYear + 1 - i)

  // Filtrar anos baseado na busca
  const filteredYears = years.filter(year =>
    year.toString().includes(yearSearch)
  )

  return (
    <header className="bg-gradient-to-r from-purple-600 via-purple-700 to-indigo-700 shadow-lg">
      <div className="flex items-center justify-between px-6 py-5">
        {/* Left side - Menu button and title */}
        <div className="flex items-center">
          <button
            onClick={onMenuClick}
            className="lg:hidden p-2 rounded-lg text-white hover:bg-white hover:bg-opacity-20 transition-all duration-200"
          >
            <Menu className="h-6 w-6" />
          </button>

          <div className="ml-4 lg:ml-0">
            <h1 className="text-3xl font-bold text-white">
              {getPageTitle()}
            </h1>
            <p className="text-purple-100 text-sm mt-1">Sistema de Gestão Financeira</p>
          </div>
        </div>

        {/* Right side - Year selector (apenas na página inicial e dashboard) */}
        <div className="flex items-center space-x-4">
          {shouldShowYearFilter && (
            <div className="relative">
              <button
                onClick={() => setShowYearDropdown(!showYearDropdown)}
                className="flex items-center px-6 py-3 bg-white bg-opacity-20 backdrop-blur-sm text-white rounded-xl hover:bg-opacity-30 transition-all duration-300 border border-white border-opacity-20 hover:border-opacity-40 shadow-lg hover:shadow-xl"
              >
                <span className="font-semibold text-lg">{selectedYear || currentYear}</span>
                <ChevronDown className="ml-2 h-5 w-5" />
              </button>

              {showYearDropdown && (
                <div className="absolute right-0 mt-3 w-48 bg-white rounded-xl shadow-2xl border border-gray-200 z-10 overflow-hidden backdrop-blur-sm">
                  <div className="p-3 border-b border-gray-200">
                    <input
                      type="text"
                      placeholder="Buscar ano..."
                      value={yearSearch}
                      onChange={(e) => setYearSearch(e.target.value)}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                  <div className="py-2 max-h-48 overflow-y-auto">
                    {filteredYears.length > 0 ? (
                      filteredYears.map((year) => (
                        <button
                          key={year}
                          onClick={() => {
                            if (onYearChange) {
                              onYearChange(year)
                            }
                            setShowYearDropdown(false)
                            setYearSearch('')
                          }}
                          className={`block w-full text-left px-5 py-3 text-sm hover:bg-purple-50 transition-colors ${
                            year === (selectedYear || currentYear)
                              ? 'bg-purple-50 font-bold text-purple-700 border-r-4 border-purple-500'
                              : 'text-gray-700 hover:text-purple-600'
                          }`}
                        >
                          {year}
                          {availableYears.includes(year) && (
                            <span className="ml-2 text-xs text-green-600">• Com dados</span>
                          )}
                        </button>
                      ))
                    ) : (
                      <div className="px-5 py-3 text-sm text-gray-500">
                        Nenhum ano encontrado
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </header>
  )
}

export default Header
