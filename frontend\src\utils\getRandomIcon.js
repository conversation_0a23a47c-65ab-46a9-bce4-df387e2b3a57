const icons = {
  // Finanças
  finance: ['💰', '💵', '💸', '🏦', '💳', '💹', '📈', '📉', '🪙', '💷', '💶', '💴'],
  
  // Casa e Moradia
  home: ['🏠', '🏡', '🏢', '🏘️', '🛋️', '🛁', '🚿', '🛏️', '🪑', '🚽', '⚡', '🔧'],
  
  // Alimentação
  food: ['🍽️', '🍳', '🥘', '🍕', '🍔', '🥪', '🥗', '🍱', '🥡', '🍜', '🥤', '☕'],
  
  // Transporte
  transport: ['🚗', '🚕', '🚌', '🚇', '🚲', '✈️', '🚅', '🛵', '⛽', '🚦', '🅿️', '🚘'],
  
  // Saúde e Bem-estar
  health: ['🏥', '💊', '🏃', '🧘', '🎗️', '⚕️', '🩺', '🫀', '🧠', '👨‍⚕️', '🦷', '👁️'],
  
  // Educação
  education: ['📚', '🎓', '✏️', '📝', '📖', '🎒', '🏫', '👨‍🏫', '👩‍🎓', '🔬', '📐', '🎨'],
  
  // Lazer e Entretenimento
  entertainment: ['🎮', '🎬', '🎭', '🎪', '🎟️', '🎫', '🎼', '🎧', '🎤', '🎪', '🎯', '🎲'],
  
  // Compras
  shopping: ['🛍️', '👕', '👖', '👗', '👟', '👜', '🛒', '🏪', '🛋️', '📱', '💻', '⌚'],
  
  // Investimentos
  investments: ['📊', '📈', '💹', '🏢', '💎', '📑', '📃', '📋', '📉', '📌', '🎯', '🔐'],
  
  // Categorias Gerais
  general: ['📁', '📂', '🗂️', '📋', '📎', '📌', '✨', '⭐', '🌟', '💫', '🔆', '📍'],
  
  // Família e Pessoal
  personal: ['👨‍👩‍👧‍👦', '👶', '🐕', '🐈', '🎁', '❤️', '🎈', '🎊', '🎉', '🎂', '👨‍👩‍👦', '👨‍👩‍👧'],
  
  // Negócios
  business: ['💼', '👔', '🏢', '📱', '💻', '📊', '📈', '📉', '📋', '✍️', '🤝', '📇']
}

export function getRandomIcon(category = null) {
  if (category && icons[category]) {
    const categoryIcons = icons[category]
    return categoryIcons[Math.floor(Math.random() * categoryIcons.length)]
  }
  
  // Se não especificar categoria, pegar de todas
  const allIcons = Object.values(icons).flat()
  return allIcons[Math.floor(Math.random() * allIcons.length)]
}

export function getIconCategories() {
  return Object.keys(icons).map(key => ({
    id: key,
    name: key.charAt(0).toUpperCase() + key.slice(1),
    icons: icons[key]
  }))
} 