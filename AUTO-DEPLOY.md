# 🚀 Auto-Deploy Setup - Sistema Sara

Este guia explica como configurar o deploy automático quando você fizer push na branch `develop`.

## 📋 Opções de Auto-Deploy

### 1. 🔄 Watchtower (Recomendado para produção)
- **Funciona**: Monitora mudanças nas imagens Docker a cada 5 minutos
- **Vantagem**: Simples, já configurado no docker-compose.yml
- **Desvantagem**: Delay de até 5 minutos

### 2. 📡 Webhook Instantâneo
- **Funciona**: GitHub chama seu servidor imediatamente após push
- **Vantagem**: Deploy instantâneo (segundos)
- **Desvantagem**: <PERSON>quer configuração adicional

### 3. 🤖 GitHub Actions + SSH
- **Funciona**: GitHub Actions faz deploy via SSH
- **Vantagem**: Mais controle, logs detalhados
- **Desvantagem**: <PERSON>quer configuração de secrets

## 🛠️ Setup Rápido (Watchtower)

### 1. Configure o .env
```bash
# Copie o exemplo
cp .env.example .env

# Configure as variáveis obrigatórias
GITHUB_TOKEN=ghp_seu_token_aqui
FRONTEND_BRANCH=develop
BACKEND_BRANCH=develop
```

### 2. Inicie em modo produção
```bash
./docker-start.bat
# Escolha opção 2 (Produção)
```

### 3. Pronto! 🎉
- Watchtower monitora automaticamente
- A cada 5 minutos verifica por atualizações
- Atualiza containers automaticamente

## ⚡ Setup Webhook Instantâneo

### 1. Configure o webhook listener
```bash
./setup-webhook.bat
```

### 2. Configure o webhook.env
```env
WEBHOOK_PORT=9000
WEBHOOK_SECRET=seu_secret_super_seguro
DEPLOY_BRANCH=develop
```

### 3. Configure no GitHub
1. Vá em **Settings** → **Webhooks** → **Add webhook**
2. **Payload URL**: `http://seu-servidor:9000/webhook`
3. **Content type**: `application/json`
4. **Secret**: mesmo valor do `WEBHOOK_SECRET`
5. **Events**: Selecione "Just the push event"

### 4. Teste o webhook
```bash
# Verifique se está funcionando
curl http://localhost:9000/health

# Veja os logs
pm2 logs sara-webhook
```

## 🔧 Setup GitHub Actions

### 1. Configure os secrets no GitHub
Vá em **Settings** → **Secrets and variables** → **Actions**:

```
DEPLOY_HOST=seu.servidor.com
DEPLOY_USER=deploy
DEPLOY_SSH_KEY=sua_chave_privada_ssh
DEPLOY_PORT=22
DEPLOY_PATH=/opt/sara
SLACK_WEBHOOK=seu_webhook_slack (opcional)
```

### 2. O workflow já está configurado
- Arquivo: `.github/workflows/auto-deploy.yml`
- Triggers: Push em `develop` ou `main`
- Ações: Build → Push → Deploy

## 📊 Monitoramento

### Watchtower
```bash
# Ver logs do Watchtower
docker logs sara-watchtower

# Status dos containers
docker-compose ps
```

### Webhook
```bash
# Status do webhook
curl http://localhost:9000/status

# Logs do PM2
pm2 logs sara-webhook

# Restart se necessário
pm2 restart sara-webhook
```

### GitHub Actions
- Vá em **Actions** no seu repositório
- Veja o status dos deploys
- Logs detalhados de cada step

## 🔍 Troubleshooting

### Watchtower não atualiza
```bash
# Verificar se o token GitHub está correto
docker-compose logs watchtower

# Forçar verificação
docker exec sara-watchtower watchtower --run-once
```

### Webhook não funciona
```bash
# Verificar se está rodando
pm2 status

# Verificar logs
pm2 logs sara-webhook

# Testar manualmente
curl -X POST http://localhost:9000/webhook \
  -H "Content-Type: application/json" \
  -d '{"ref":"refs/heads/develop","commits":[{"id":"test"}]}'
```

### GitHub Actions falha
1. Verifique os secrets configurados
2. Verifique se o servidor SSH está acessível
3. Verifique os logs na aba Actions

## 🎯 Fluxo Recomendado

### Para desenvolvimento:
1. Use `docker-compose.local.yml` (modo 1)
2. Desenvolva normalmente
3. Faça push para `develop`

### Para produção:
1. Use `docker-compose.yml` (modo 2)
2. Configure Watchtower + Webhook
3. Push em `develop` → Deploy automático

## 📝 Comandos Úteis

```bash
# Iniciar produção
./docker-start.bat

# Parar tudo
docker-compose down

# Ver logs
docker-compose logs -f

# Limpar imagens antigas
docker image prune -f

# Status do webhook
curl http://localhost:9000/health

# Forçar deploy manual
git pull && docker-compose up -d --build --force-recreate
```

## 🔐 Segurança

1. **Webhook Secret**: Use um valor forte e único
2. **GitHub Token**: Use token com permissões mínimas
3. **SSH Keys**: Use chaves específicas para deploy
4. **Firewall**: Limite acesso à porta do webhook

## 📈 Próximos Passos

1. Configure notificações Slack/Discord
2. Adicione testes automatizados
3. Configure staging environment
4. Implemente rollback automático
5. Monitore performance pós-deploy
