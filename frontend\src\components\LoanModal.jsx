import { useState, useEffect } from 'react'
import { X, DollarSign, Calendar, FileText, Upload, User, Building2, Percent, Hash } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { loanService, loanUtils } from '../services/loanService'
import toast from 'react-hot-toast'

function LoanModal({ isOpen, onClose, contact, contacts, banks, onSuccess }) {
  const [loading, setLoading] = useState(false)
  const [receiptFile, setReceiptFile] = useState(null)
  const [calculatedInstallment, setCalculatedInstallment] = useState(0)

  const { register, handleSubmit, reset, watch, formState: { errors } } = useForm()

  const watchedValues = watch(['totalAmount', 'installments', 'interestRate', 'type'])

  useEffect(() => {
    if (isOpen) {
      reset({
        contactId: contact?.id || '',
        title: '',
        type: 'LOAN_GIVEN',
        loanType: 'MONEY',
        totalAmount: '',
        installments: 1,
        interestRate: 0,
        startDate: new Date().toISOString().split('T')[0],
        expectedEndDate: '',
        bankId: '',
        notes: ''
      })
      setReceiptFile(null)
    }
  }, [isOpen, contact, reset])

  // Calcular valor da parcela automaticamente
  useEffect(() => {
    const [totalAmount, installments, interestRate] = watchedValues

    if (totalAmount && installments) {
      const total = parseFloat(totalAmount) || 0
      const parcelas = parseInt(installments) || 1
      const juros = parseFloat(interestRate) || 0

      const totalWithInterest = total * (1 + juros / 100)
      const installmentValue = totalWithInterest / parcelas

      setCalculatedInstallment(installmentValue)
    }
  }, [watchedValues])

  const handleReceiptChange = (e) => {
    const file = e.target.files[0]
    if (file) {
      if (file.size > 10 * 1024 * 1024) { // 10MB
        toast.error('Arquivo muito grande. Máximo 10MB.')
        return
      }
      setReceiptFile(file)
    }
  }

  const onSubmit = async (data) => {
    try {
      setLoading(true)

      const formData = {
        contactId: data.contactId,
        title: data.title,
        type: data.type,
        loanType: data.loanType,
        totalAmount: parseFloat(data.totalAmount),
        installments: parseInt(data.installments),
        interestRate: parseFloat(data.interestRate),
        startDate: data.startDate,
        expectedEndDate: data.expectedEndDate || null,
        bankId: data.bankId,
        notes: data.notes || null
      }

      if (receiptFile) {
        formData.receipt = receiptFile
      }

      await loanService.createLoan(formData)
      toast.success('Empréstimo criado com sucesso!')

      onSuccess()
      onClose()
    } catch (error) {
      console.error('Erro ao criar empréstimo:', error)
      toast.error('Erro ao criar empréstimo')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-green-600 to-green-700 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <DollarSign className="h-5 w-5" />
              </div>
              <div>
                <h2 className="text-xl font-bold">Novo Empréstimo</h2>
                <p className="text-green-100 text-sm">
                  {contact ? `Para ${contact.name}` : 'Selecione um contato'}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
          {/* Contato */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Contato *
            </label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <select
                {...register('contactId', { required: 'Selecione um contato' })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="">Selecione um contato</option>
                {contacts.map((c) => (
                  <option key={c.id} value={c.id}>
                    {c.name}
                  </option>
                ))}
              </select>
            </div>
            {errors.contactId && (
              <p className="mt-1 text-sm text-red-600">{errors.contactId.message}</p>
            )}
          </div>

          {/* Título */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Título/Descrição *
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                {...register('title', { required: 'Título é obrigatório' })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Ex: Empréstimo para emergência"
              />
            </div>
            {errors.title && (
              <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
            )}
          </div>

          {/* Tipo e Tipo de Empréstimo */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tipo *
              </label>
              <select
                {...register('type', { required: 'Selecione o tipo' })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="LOAN_GIVEN">Emprestei (Saída)</option>
                <option value="LOAN_RECEIVED">Peguei Emprestado (Entrada)</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Forma *
              </label>
              <select
                {...register('loanType', { required: 'Selecione a forma' })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="MONEY">Dinheiro</option>
                <option value="CREDIT_CARD">Compra no Cartão</option>
                <option value="OTHER">Outro</option>
              </select>
            </div>
          </div>

          {/* Valor e Parcelas */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Valor Total *
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="number"
                  step="0.01"
                  {...register('totalAmount', {
                    required: 'Valor é obrigatório',
                    min: { value: 0.01, message: 'Valor deve ser maior que zero' }
                  })}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="0,00"
                />
              </div>
              {errors.totalAmount && (
                <p className="mt-1 text-sm text-red-600">{errors.totalAmount.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Parcelas
              </label>
              <div className="relative">
                <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="number"
                  min="1"
                  {...register('installments', {
                    min: { value: 1, message: 'Mínimo 1 parcela' }
                  })}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="1"
                />
              </div>
            </div>
          </div>

          {/* Taxa de Juros e Valor da Parcela */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Taxa de Juros (%)
              </label>
              <div className="relative">
                <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  {...register('interestRate')}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="0,00"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Valor por Parcela
              </label>
              <div className="px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-700 font-medium">
                {loanUtils.formatCurrency(calculatedInstallment)}
              </div>
            </div>
          </div>

          {/* Datas */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Data do Empréstimo *
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="date"
                  {...register('startDate', { required: 'Data é obrigatória' })}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              {errors.startDate && (
                <p className="mt-1 text-sm text-red-600">{errors.startDate.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Previsão de Quitação
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="date"
                  {...register('expectedEndDate')}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Banco */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Banco de Origem *
            </label>
            <p className="text-sm text-gray-600 mb-3 bg-blue-50 p-3 rounded-lg border border-blue-200">
              💰 <strong>Importante:</strong> O dinheiro será {watch('type') === 'LOAN_GIVEN' ? 'debitado' : 'creditado'} do banco selecionado abaixo.
            </p>
            <div className="relative">
              <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <select
                {...register('bankId', { required: 'Selecione um banco' })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="">Selecione o banco de origem</option>
                {banks.map((bank) => (
                  <option key={bank.id} value={bank.id}>
                    {bank.icon} {bank.name} - Saldo: {loanUtils.formatCurrency(bank.currentBalance)}
                  </option>
                ))}
              </select>
            </div>
            {errors.bankId && (
              <p className="mt-1 text-sm text-red-600">{errors.bankId.message}</p>
            )}
            <p className="mt-2 text-sm text-gray-500">
              <span className="font-medium text-gray-700">Importante:</span> O valor do empréstimo {watch('type') === 'LOAN_GIVEN' ? 'será debitado' : 'será creditado'} na conta do banco selecionado.
            </p>
          </div>

          {/* Comprovante */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Comprovante
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-green-400 transition-colors">
              <input
                type="file"
                onChange={handleReceiptChange}
                className="hidden"
                id="receipt-upload"
                accept="image/*,.pdf"
              />
              <label htmlFor="receipt-upload" className="cursor-pointer">
                <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">
                  {receiptFile ? receiptFile.name : 'Clique para adicionar comprovante'}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  PNG, JPG ou PDF até 10MB
                </p>
              </label>
            </div>
          </div>

          {/* Notas */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Observações
            </label>
            <textarea
              {...register('notes')}
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
              placeholder="Informações adicionais sobre o empréstimo..."
            />
          </div>

          {/* Botões */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Criando...
                </>
              ) : (
                'Criar Empréstimo'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default LoanModal
