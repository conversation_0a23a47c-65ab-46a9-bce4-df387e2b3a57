version: '3.8'

services:
  frontend:
    build:
      context: https://${GITHUB_TOKEN}@github.com/<PERSON>-<PERSON>ulin/sara.git#${FRONTEND_BRANCH:-develop}:frontend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=${NODE_ENV:-production}
        - REACT_APP_API_URL=${REACT_APP_API_URL:-http://backend:5000}
        - REACT_APP_TABLE_FONT_SIZE=${REACT_APP_TABLE_FONT_SIZE:-14px}
        - REACT_APP_TABLE_PADDING=${REACT_APP_TABLE_PADDING:-12px}
        - REACT_APP_CONTAINER_MAX_WIDTH=${REACT_APP_CONTAINER_MAX_WIDTH:-100%}
        - REACT_APP_TABLE_BG_COLOR=${REACT_APP_TABLE_BG_COLOR:-#f8f9fa}
    container_name: sara-frontend
    restart: always
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    depends_on:
      - backend
    networks:
      - sara-network
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  backend:
    build:
      context: https://${GITHUB_TOKEN}@github.com/Daniel-Shaulin/sara.git#${BACKEND_BRANCH:-develop}:backend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=${NODE_ENV:-production}
    container_name: sara-backend
    restart: always
    ports:
      - "${BACKEND_PORT:-5000}:5000"
    environment:
      - DATABASE_URL=file:/app/prisma/dev.db
      - NODE_ENV=${NODE_ENV:-production}
      - JWT_SECRET=${JWT_SECRET:-your_jwt_secret}
      - PORT=5000
      - CLOUDINARY_CLOUD_NAME=${CLOUDINARY_CLOUD_NAME}
      - CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY}
      - CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET}
    volumes:
      - backend_data:/app/prisma
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - sara-network
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  watchtower:
    image: containrrr/watchtower
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ${DOCKER_CONFIG:-$HOME/.docker/config.json}:/config.json
    command: --interval 300 --cleanup --include-restarting --include-stopped --revive-stopped
    restart: always
    environment:
      - WATCHTOWER_LABEL_ENABLE=true
      - DOCKER_CONFIG=/config.json
      - WATCHTOWER_POLL_INTERVAL=300
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_INCLUDE_RESTARTING=true
      - WATCHTOWER_INCLUDE_STOPPED=true
      - WATCHTOWER_REVIVE_STOPPED=true
      - WATCHTOWER_NOTIFICATIONS=${WATCHTOWER_NOTIFICATIONS:-}
      - WATCHTOWER_NOTIFICATION_URL=${WATCHTOWER_NOTIFICATION_URL:-}
      - WATCHTOWER_DEBUG=${WATCHTOWER_DEBUG:-false}
    networks:
      - sara-network

networks:
  sara-network:
    driver: bridge

volumes:
  backend_data: 