import React from 'react'
import Sidebar from '../components/Sidebar'

function LoansTest() {
  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      
      <div className="flex-1 overflow-auto">
        <div className="p-8">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              🎉 Sistema de Empréstimos
            </h1>
            
            <div className="space-y-4">
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h2 className="text-lg font-semibold text-green-800 mb-2">
                  ✅ Frontend Funcionando!
                </h2>
                <p className="text-green-700">
                  Se você está vendo esta página, o frontend está carregando corretamente.
                </p>
              </div>

              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h2 className="text-lg font-semibold text-blue-800 mb-2">
                  📋 Próximos Passos
                </h2>
                <ul className="text-blue-700 space-y-1">
                  <li>• Verificar se o backend está rodando</li>
                  <li>• Testar conexão com a API</li>
                  <li>• Implementar funcionalidades gradualmente</li>
                </ul>
              </div>

              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h2 className="text-lg font-semibold text-yellow-800 mb-2">
                  🔧 Status do Sistema
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Frontend:</span>
                    <span className="ml-2 text-green-600">✅ Funcionando</span>
                  </div>
                  <div>
                    <span className="font-medium">Sidebar:</span>
                    <span className="ml-2 text-green-600">✅ Carregada</span>
                  </div>
                  <div>
                    <span className="font-medium">Roteamento:</span>
                    <span className="ml-2 text-green-600">✅ Funcionando</span>
                  </div>
                  <div>
                    <span className="font-medium">Estilos:</span>
                    <span className="ml-2 text-green-600">✅ Aplicados</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">Contatos</h3>
                  <p className="text-2xl font-bold text-blue-600">0</p>
                  <p className="text-sm text-gray-600">Em desenvolvimento</p>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">Empréstimos</h3>
                  <p className="text-2xl font-bold text-green-600">0</p>
                  <p className="text-sm text-gray-600">Em desenvolvimento</p>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">Pagamentos</h3>
                  <p className="text-2xl font-bold text-purple-600">0</p>
                  <p className="text-sm text-gray-600">Em desenvolvimento</p>
                </div>
              </div>

              <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <h2 className="text-lg font-semibold text-gray-800 mb-2">
                  🚀 Comandos Úteis
                </h2>
                <div className="space-y-2 text-sm font-mono">
                  <div className="bg-gray-800 text-green-400 p-2 rounded">
                    npm run dev
                  </div>
                  <div className="bg-gray-800 text-green-400 p-2 rounded">
                    update-schema.bat
                  </div>
                  <div className="bg-gray-800 text-green-400 p-2 rounded">
                    debug-frontend.bat
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LoansTest
