import api from './api'

export const piggyBankService = {
  // Listar todos os cofrinhos
  async getPiggyBanks() {
    const response = await api.get('/piggy-banks')
    return response.data
  },

  // Criar cofrinho
  async createPiggyBank(piggyBankData) {
    const response = await api.post('/piggy-banks', piggyBankData)
    return response.data
  },

  // Atualizar cofrinho
  async updatePiggyBank(id, piggyBankData) {
    const response = await api.put(`/piggy-banks/${id}`, piggyBankData)
    return response.data
  },

  // Deletar cofrinho
  async deletePiggyBank(id) {
    const response = await api.delete(`/piggy-banks/${id}`)
    return response.data
  },

  // Adicionar valor ao cofrinho
  async addAmount(id, amount) {
    const response = await api.post(`/piggy-banks/${id}/add`, { amount })
    return response.data
  },

  // Retirar valor do cofrinho
  async withdrawAmount(id, amount) {
    const response = await api.post(`/piggy-banks/${id}/withdraw`, { amount })
    return response.data
  },

  // Atualizar CDI de todos os cofrinhos
  async updateCdi(cdiRate) {
    const response = await api.post('/piggy-banks/update-cdi', { cdiRate })
    return response.data
  },

  // Aplicar rendimento CDI
  async applyCdi() {
    const response = await api.post('/piggy-banks/apply-cdi')
    return response.data
  }
}
