import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  Home,
  CreditCard,
  Tag,
  Pie<PERSON>hart,
  Settings,
  LogOut,
  DollarSign,
  BarChart3,
  Wallet,
  Heart,
  User,
  ChevronRight
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'

function Sidebar({ isOpen, onClose }) {
  const location = useLocation()
  const { logout, user } = useAuth()
  const [showAccountSettings, setShowAccountSettings] = useState(false)

  const menuItems = [
    { path: '/dashboard', icon: Home, label: 'Página Inicial' },
    { path: '/dashboard-analytics', icon: BarChart3, label: 'Dashboard' },
    { path: '/transactions', icon: CreditCard, label: 'Transações' },
    { path: '/categories', icon: Tag, label: 'Categorias' },
    { path: '/banks', icon: Wallet, label: 'Bancos' },
    { path: '/loans', icon: Heart, label: 'Empréstimos' },
    { path: '/reports', icon: Pie<PERSON><PERSON>, label: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { path: '/settings', icon: Settings, label: '<PERSON>figuraçõ<PERSON>' },
  ]

  const handleLogout = () => {
    logout()
    onClose()
  }

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:flex lg:flex-shrink-0">
        <div className="flex flex-col w-64">
          <div className="flex flex-col flex-grow bg-sidebar-bg overflow-y-auto">
            {/* Logo */}
            <div className="flex items-center flex-shrink-0 px-4 py-6">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
                <div className="ml-3">
                  <span className="text-xl font-bold text-white">SARA</span>
                  <p className="text-xs text-gray-300">Sistema de Acompanhamento</p>
                  <p className="text-xs text-gray-300">de Recursos e Aplicações</p>
                </div>
              </div>
            </div>

            {/* Navigation */}
            <nav className="flex-1 px-2 pb-4 space-y-1">
              {menuItems.map((item) => {
                const Icon = item.icon
                const isActive = location.pathname === item.path

                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={`sidebar-item ${isActive ? 'active' : ''}`}
                  >
                    <Icon className="mr-3 h-5 w-5" />
                    {item.label}
                  </Link>
                )
              })}
            </nav>

            {/* User Profile */}
            <div className="flex-shrink-0 px-2 pb-2">
              <div className="bg-gray-800 rounded-lg p-3 mb-2">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center cursor-pointer hover:from-blue-600 hover:to-purple-700 transition-all duration-200"
                       onClick={() => setShowAccountSettings(true)}>
                    <span className="text-white text-sm font-bold">
                      {user?.name?.charAt(0).toUpperCase() || 'U'}
                    </span>
                  </div>
                  <div className="ml-3 flex-1 min-w-0">
                    <p className="text-sm font-medium text-white truncate">
                      {user?.name || 'Usuário'}
                    </p>
                    <p className="text-xs text-gray-300 truncate">
                      {user?.email || '<EMAIL>'}
                    </p>
                  </div>
                  <button
                    onClick={() => setShowAccountSettings(true)}
                    className="ml-2 p-1 text-gray-400 hover:text-white transition-colors"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Logout */}
            <div className="flex-shrink-0 px-2 pb-4">
              <button
                onClick={handleLogout}
                className="sidebar-item w-full text-left"
              >
                <LogOut className="mr-3 h-5 w-5" />
                Sair
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-sidebar-bg transform ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      } transition-transform duration-300 ease-in-out lg:hidden`}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center flex-shrink-0 px-4 py-6">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="ml-3">
                <span className="text-xl font-bold text-white">SARA</span>
                <p className="text-xs text-gray-300">Sistema de Acompanhamento</p>
                <p className="text-xs text-gray-300">de Recursos e Aplicações</p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-2 pb-4 space-y-1">
            {menuItems.map((item) => {
              const Icon = item.icon
              const isActive = location.pathname === item.path

              return (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={onClose}
                  className={`sidebar-item ${isActive ? 'active' : ''}`}
                >
                  <Icon className="mr-3 h-5 w-5" />
                  {item.label}
                </Link>
              )
            })}
          </nav>

          {/* User Profile */}
          <div className="flex-shrink-0 px-2 pb-2">
            <div className="bg-gray-800 rounded-lg p-3 mb-2">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center cursor-pointer hover:from-blue-600 hover:to-purple-700 transition-all duration-200"
                     onClick={() => setShowAccountSettings(true)}>
                  <span className="text-white text-sm font-bold">
                    {user?.name?.charAt(0).toUpperCase() || 'U'}
                  </span>
                </div>
                <div className="ml-3 flex-1 min-w-0">
                  <p className="text-sm font-medium text-white truncate">
                    {user?.name || 'Usuário'}
                  </p>
                  <p className="text-xs text-gray-300 truncate">
                    {user?.email || '<EMAIL>'}
                  </p>
                </div>
                <button
                  onClick={() => setShowAccountSettings(true)}
                  className="ml-2 p-1 text-gray-400 hover:text-white transition-colors"
                >
                  <ChevronRight className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Logout */}
          <div className="flex-shrink-0 px-2 pb-4">
            <button
              onClick={handleLogout}
              className="sidebar-item w-full text-left"
            >
              <LogOut className="mr-3 h-5 w-5" />
              Sair
            </button>
          </div>
        </div>
      </div>

      {/* Modal de Configurações da Conta */}
      {showAccountSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-xl w-full max-w-md">
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4 text-white rounded-t-2xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <User className="h-6 w-6" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold">Configurações da Conta</h2>
                    <p className="text-blue-100 text-sm">Gerencie suas informações pessoais</p>
                  </div>
                </div>
                <button
                  onClick={() => setShowAccountSettings(false)}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                >
                  <span className="text-xl">×</span>
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="space-y-6">
                {/* Foto do Perfil */}
                <div className="text-center">
                  <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 cursor-pointer hover:from-blue-600 hover:to-purple-700 transition-all duration-200">
                    <span className="text-white text-2xl font-bold">
                      {user?.name?.charAt(0).toUpperCase() || 'U'}
                    </span>
                  </div>
                  <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                    Alterar foto
                  </button>
                </div>

                {/* Informações do Usuário */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nome
                    </label>
                    <input
                      type="text"
                      value={user?.name || ''}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                      readOnly
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email
                    </label>
                    <input
                      type="email"
                      value={user?.email || ''}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                      readOnly
                    />
                  </div>
                </div>

                {/* Ações */}
                <div className="space-y-3">
                  <button className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                    Editar Informações
                  </button>
                  <button className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium">
                    Alterar Senha
                  </button>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="border-t border-gray-200 px-6 py-4 bg-gray-50 rounded-b-2xl">
              <div className="flex justify-end">
                <button
                  onClick={() => setShowAccountSettings(false)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Fechar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default Sidebar
