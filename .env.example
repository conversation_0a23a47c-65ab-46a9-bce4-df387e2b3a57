# Docker Compose Environment Variables

# Node Environment
NODE_ENV=production

# Frontend Configuration
FRONTEND_PORT=3000
REACT_APP_API_URL=http://backend:5000
REACT_APP_TABLE_FONT_SIZE=14px
REACT_APP_TABLE_PADDING=12px
REACT_APP_CONTAINER_MAX_WIDTH=100%
REACT_APP_TABLE_BG_COLOR=#f8f9fa

# Backend Configuration
BACKEND_PORT=5000
JWT_SECRET=your_jwt_secret_change_this_in_production

# Cloudinary Configuration (for image uploads)
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# GitHub Configuration (required for production builds)
GITHUB_TOKEN=your_github_token_here
FRONTEND_BRANCH=develop
BACKEND_BRANCH=develop

# Watchtower Configuration (auto-deploy)
WATCHTOWER_NOTIFICATIONS=slack
WATCHTOWER_NOTIFICATION_URL=slack://token@channel
WATCHTOWER_DEBUG=false
DOCKER_CONFIG=/root/.docker/config.json

# Webhook Configuration (for instant deploy)
WEBHOOK_PORT=9000
WEBHOOK_SECRET=your_webhook_secret_change_this
DEPLOY_BRANCH=develop

# Deploy Server Configuration (for GitHub Actions)
# DEPLOY_HOST=your.server.com
# DEPLOY_USER=deploy
# DEPLOY_SSH_KEY=your_private_key
# DEPLOY_PORT=22
# DEPLOY_PATH=/opt/sara
# SLACK_WEBHOOK=your_slack_webhook_url
