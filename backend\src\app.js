const express = require('express')
const cors = require('cors')
const morgan = require('morgan')
const path = require('path')

const app = express()

// Middlewares
app.use(cors())
app.use(express.json())
app.use(express.urlencoded({ extended: true }))
app.use(morgan('dev'))

// Servir arquivos estáticos
app.use('/uploads', express.static(path.join(__dirname, '../uploads')))

// Importar rotas
const authRoutes = require('./routes/auth')
const categoriesRoutes = require('./routes/categories')
const transactionsRoutes = require('./routes/transactions')
const banksRoutes = require('./routes/banks')
const paymentMethodsRoutes = require('./routes/paymentMethods')
const savingsRoutes = require('./routes/savings')
const subscriptionsRoutes = require('./routes/subscriptions')
const contactsRoutes = require('./routes/contacts')
const loansRoutes = require('./routes/loans')
const loanPaymentsRoutes = require('./routes/loanPayments')
const dashboardRoutes = require('./routes/dashboard')
const tagsRoutes = require('./routes/tags')
const routinesRoutes = require('./routes/routines')
const notificationsRoutes = require('./routes/notifications')
const transactionTemplatesRoutes = require('./routes/transactionTemplates')
const savingsRoutes = require('./routes/savings')

// Registrar rotas
app.use('/api/auth', authRoutes)
app.use('/api/categories', categoriesRoutes)
app.use('/api/transactions', transactionsRoutes)
app.use('/api/banks', banksRoutes)
app.use('/api/payment-methods', paymentMethodsRoutes)
app.use('/api/savings', savingsRoutes)
app.use('/api/subscriptions', subscriptionsRoutes)
app.use('/api/contacts', contactsRoutes)
app.use('/api/loans', loansRoutes)
app.use('/api/loan-payments', loanPaymentsRoutes)
app.use('/api/dashboard', dashboardRoutes)
app.use('/api/tags', tagsRoutes)
app.use('/api/routines', routinesRoutes)
app.use('/api/notifications', notificationsRoutes)
app.use('/api/transaction-templates', transactionTemplatesRoutes)
app.use('/api/savings', savingsRoutes)

// Rota para verificar se a API está funcionando
app.get('/', (req, res) => {
  res.json({ message: 'API está funcionando!' })
})

// Tratamento de rotas não encontradas
app.use((req, res) => {
  console.log(req.url);
  res.status(404).json({ error: 'Rota não encontrada' })
})

// Tratamento de erros
app.use((err, req, res, next) => {
  console.error(err.stack)
  res.status(500).json({ error: 'Erro interno do servidor' })
})

module.exports = app 