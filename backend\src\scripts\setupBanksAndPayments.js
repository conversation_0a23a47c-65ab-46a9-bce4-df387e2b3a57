const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function setupBanksAndPayments() {
  try {
    console.log('🏦 Configurando sistema de bancos e formas de pagamento...');

    // Buscar todos os usuários
    const users = await prisma.user.findMany();

    for (const user of users) {
      console.log(`📝 Configurando para usuário: ${user.email}`);

      // Criar bancos padrão
      const defaultBanks = [
        {
          name: 'Conta Corrente',
          icon: '🏦',
          color: '#3B82F6',
          initialBalance: 0,
          currentBalance: 0,
          userId: user.id
        },
        {
          name: '<PERSON><PERSON><PERSON><PERSON>',
          icon: '🐷',
          color: '#22C55E',
          initialBalance: 0,
          currentBalance: 0,
          userId: user.id
        },
        {
          name: '<PERSON><PERSON><PERSON>',
          icon: '💰',
          color: '#F59E0B',
          initialBalance: 0,
          currentBalance: 0,
          userId: user.id
        }
      ];

      for (const bankData of defaultBanks) {
        const existingBank = await prisma.bank.findFirst({
          where: {
            userId: user.id,
            name: bankData.name
          }
        });

        if (!existingBank) {
          await prisma.bank.create({ data: bankData });
          console.log(`  ✅ Banco criado: ${bankData.name}`);
        }
      }

      // Criar formas de pagamento padrão
      const defaultPaymentMethods = [
        {
          name: 'Dinheiro',
          icon: '💵',
          color: '#22C55E',
          type: 'CASH',
          userId: user.id
        },
        {
          name: 'Cartão de Crédito',
          icon: '💳',
          color: '#3B82F6',
          type: 'CREDIT',
          userId: user.id
        },
        {
          name: 'Cartão de Débito',
          icon: '💳',
          color: '#EF4444',
          type: 'DEBIT',
          userId: user.id
        },
        {
          name: 'PIX',
          icon: '📱',
          color: '#8B5CF6',
          type: 'PIX',
          userId: user.id
        },
        {
          name: 'Transferência',
          icon: '🔄',
          color: '#F59E0B',
          type: 'TRANSFER',
          userId: user.id
        },
        {
          name: 'Boleto',
          icon: '📄',
          color: '#6B7280',
          type: 'OTHER',
          userId: user.id
        }
      ];

      for (const methodData of defaultPaymentMethods) {
        const existingMethod = await prisma.paymentMethod.findFirst({
          where: {
            userId: user.id,
            name: methodData.name
          }
        });

        if (!existingMethod) {
          await prisma.paymentMethod.create({ data: methodData });
          console.log(`  ✅ Forma de pagamento criada: ${methodData.name}`);
        }
      }
    }

    console.log('✅ Sistema de bancos e formas de pagamento configurado com sucesso!');
  } catch (error) {
    console.error('❌ Erro ao configurar sistema:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  setupBanksAndPayments();
}

module.exports = setupBanksAndPayments;
